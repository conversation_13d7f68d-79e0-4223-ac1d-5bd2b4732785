import { AdType, AtvTypeModel, BreakConnector, IConfiguration } from 'adpod-tools';
import { AdserverAd, AdserverAdPreParse, RequestHeaders } from '.';
import { RequestMacroParams } from '../scripts/configuration/injectReqParams/requestMacroParams';

export type DaiAdsProviderCommonType = {
  type: AdType;
  position: number;
  duration: number;
  adId?: string;
};

export type DaiAdsProviderAtvSpotsType = DaiAdsProviderCommonType & {
  adrequest: string;
};

export type DaiAdsProviderBreakType = DaiAdsProviderCommonType & {
  slotRestrictions?: string[];
  atvType?: AtvTypeModel;
};

export type DaiAdsProviderInput = {
  configuration: IConfiguration;
  headers: RequestHeaders;
  connector: BreakConnector;
  requestMacroParams: RequestMacroParams;
  custParams?: string;
  ip?: string;
  ua?: string | string[];
};

export type UltimateDaiAdsProviderInput = DaiAdsProviderInput & {
  providerConnector: BreakConnector;
};

export type DaiAdsRequestCommon = {
  headers: RequestHeaders;
  ip?: string;
  ua?: string | string[];
  gdpr?: string;
  gdprConsent?: string;
  custParams?: string;
  uid?: string;
  npa?: string;
};

export type DaiAdsConfigCommon = {
  breakId: string;
  blockDuration: number;
  version: string;
  channel: string;
};

export type DaiAdsProviderOutputCommon = {
  request: DaiAdsRequestCommon;
  config: DaiAdsConfigCommon;
};

export type DaiAdsProviderOutput = DaiAdsProviderOutputCommon & {
  config: DaiAdsConfigCommon & {
    connector: BreakConnector;
    atvSpots: DaiAdsProviderAtvSpotsType[];
  };
};

export type DaiAdsProviderOutputBreak = DaiAdsProviderOutputCommon & {
  config: DaiAdsConfigADOBreak;
};

export type DaiAdsConfigADOBreak = DaiAdsConfigCommon & {
  spots: DaiAdsProviderBreakType[];
  breakConnector: BreakConnector;
  exactLength: boolean;
  breakAdRequest: string;
  adUnitId: string | undefined;
};

export type logRequestStatsInput = {
  startFetchTime: number;
  endFetchTime: number;
  requestedSlots: number;
  version: string;
  connector: BreakConnector;
  channel: string;
  isProgrammatic?: boolean;
};

export type logRequestStatsInputATV = logRequestStatsInput & {
  filledSlots: number;
  returnedSlots: number;
};

export type logRequestStatsInputBreak = logRequestStatsInput & {
  isWithExactLength: boolean;
  finalResponse: AdserverAd[];
  adUnitId: string | undefined;
};

export type fetchAdsOutput = {
  startFetchTime: number;
  endFetchTime: number;
  daiAds: AdserverAdPreParse[];
};
