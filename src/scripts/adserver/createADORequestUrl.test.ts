import { DaiAdsRequestCommon } from '../../interfaces';
import { createADORequestUrl } from './createADORequestUrl';

const adServerUrl =
  'https://tvn.adocean.pl/ad.xml?aocodetype=1/id=A1w3VxpUJ7UcI7sa6HfkBq7OAyJJ31IAAF6sV.rSP2L.S7/p=1/bid=7515456263876321/mid1dur=15/mid1maxdur=15/mid1mindur=15';
const adServerURLWithoutDefaultId =
  'https://tvn.adocean.pl/ad.xml?aocodetype=1/p=1/bid=7515456263876321/mid1dur=15/mid1maxdur=15/mid1mindur=15';

describe('createADORequestUrl', () => {
  describe('cust_params tests', () => {
    test('should return base URL', () => {
      const params: DaiAdsRequestCommon = { headers: {} };
      expect(createADORequestUrl(adServerUrl, params)).toEqual(adServerUrl);
    });

    test('should return URL with appeded cust_params (encoded, 1 param)', () => {
      const params: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone%3DvalueOne'
      };

      expect(createADORequestUrl(adServerUrl, params)).toEqual(
        `${adServerUrl}/paramone=valueOne`
      );
    });

    test('should return URL with appeded cust_params (decoded, 1 param)', () => {
      const params: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone=valueOne'
      };
      expect(createADORequestUrl(adServerUrl, params)).toEqual(
        `${adServerUrl}/paramone=valueOne`
      );
    });

    test('should return URL with appeded cust_params (encoded, 2 params)', () => {
      const params: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone%3DvalueOne%26paramtwo%3DvalueTwo'
      };
      expect(createADORequestUrl(adServerUrl, params)).toEqual(
        `${adServerUrl}/paramone=valueOne/paramtwo=valueTwo`
      );
    });

    test('should return URL with appeded cust_params (decoded, 2 params)', () => {
      const params: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone=valueOne&paramtwo=valueTwo'
      };
      expect(createADORequestUrl(adServerUrl, params)).toEqual(
        `${adServerUrl}/paramone=valueOne/paramtwo=valueTwo`
      );
    });

    describe('should repleace URL id with cust_params ado_id (encoded string)', () => {
      test('base', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id%3DidValue'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerURLWithoutDefaultId}/id=idValue`
        );
      });

      test('with additional params before it', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'paramone%3DvalueOne%26ado_id%3DidValue'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue`
        );
      });

      test('with additional params after it', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id%3DidValue%26paramone%3DvalueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue`
        );
      });
    });

    describe('should repleace URL id with cust_params ado_id (decoded string)', () => {
      test('base', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id=idValue'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerURLWithoutDefaultId}/id=idValue`
        );
      });

      test('with additional params before it', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'paramone=valueOne&ado_id=idValue'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue`
        );
      });

      test('with additional params after it', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id=idValue&paramone=valueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue`
        );
      });
    });

    describe('should not replace URL id with cust_params param that contains "id" within it (decoded)', () => {
      test('wrong prefix', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid=valueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/fooid=valueOne`
        );
      });
      test('wrong prefix and wrong suffix', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooidfoo=valueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/fooidfoo=valueOne`
        );
      });
      test('wrong suffix', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'idfoo=valueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/idfoo=valueOne`
        );
      });
      test('every possible wrong combination', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid=valueOne&idfoo=valueTwo&fooidfoo=valueThree'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/fooid=valueOne/idfoo=valueTwo/fooidfoo=valueThree`
        );
      });
    });

    describe('should not replace URL id with cust_params param that contains "id" within it (encoded)', () => {
      test('wrong prefix', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid%3DvalueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/fooid=valueOne`
        );
      });
      test('wrong prefix and wrong suffix', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooidfoo%3DvalueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/fooidfoo=valueOne`
        );
      });
      test('wrong suffix', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'idfoo%3DvalueOne'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/idfoo=valueOne`
        );
      });
      test('every possible wrong combination', () => {
        const params: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid%3DvalueOne%26idfoo%3DvalueTwo%26fooidfoo%3DvalueThree'
        };

        expect(createADORequestUrl(adServerUrl, params)).toEqual(
          `${adServerUrl}/fooid=valueOne/idfoo=valueTwo/fooidfoo=valueThree`
        );
      });
    });
  });

  describe('cust_params + uid tests', () => {
    describe('should use uid param value if uid param passes as a part of cust_params', () => {
      test('uid is in cust_params (decoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'uid=uidValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/aouserid=123`
        );
      });

      test('uid is in cust_params (encoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'uid%3DuidValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/aouserid=123`
        );
      });

      test('uid is in cust_params with additional params before uid (decoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'paramOne=paramOneValue&uid=uidValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/paramOne=paramOneValue/aouserid=123`
        );
      });

      test('uid is in cust_params with additional params before uid (encoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'paramOne%3DparamOneValue%26uid%3DuidValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/paramOne=paramOneValue/aouserid=123`
        );
      });

      test('uid is in cust_params with additional params after uid (decoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'uid=uidValue&paramOne=paramOneValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/paramOne=paramOneValue/aouserid=123`
        );
      });

      test('uid is in cust_params with additional params after uid (encoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'uid%3DuidValue%26paramOne%3DparamOneValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/paramOne=paramOneValue/aouserid=123`
        );
      });

      test('uid is in cust_params with more additional params after uid (encoded)', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'uid%3DuidValue%26paramOne%3DparamOneValue%26a%3D1%26b%3D2',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/paramOne=paramOneValue/a=1/b=2/aouserid=123`
        );
      });
    });

    test('should return URL with appeded cust_params (encoded, 1 param) and uid', () => {
      const paramsWithUid: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone%3DvalueOne',
        uid: '123'
      };

      expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
        `${adServerUrl}/paramone=valueOne/aouserid=123`
      );
    });

    test('should return URL with appeded cust_params (decoded, 1 param) and uid', () => {
      const paramsWithUid: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone=valueOne',
        uid: '123'
      };

      expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
        `${adServerUrl}/paramone=valueOne/aouserid=123`
      );
    });

    test('should return URL with appeded uid if cust_params not provided', () => {
      const paramsWithUid: DaiAdsRequestCommon = {
        headers: {},
        uid: '123'
      };

      expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
        `${adServerUrl}/aouserid=123`
      );
    });

    test('should return URL with appeded cust_params (encoded, 2 params) and uid', () => {
      const paramsWithUid: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone%3DvalueOne%26paramtwo%3DvalueTwo',
        uid: '123'
      };

      expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
        `${adServerUrl}/paramone=valueOne/paramtwo=valueTwo/aouserid=123`
      );
    });

    test('should return URL with appeded cust_params (decoded, 2 params) and uid', () => {
      const paramsWithUid: DaiAdsRequestCommon = {
        headers: {},
        custParams: 'paramone=valueOne&paramtwo=valueTwo',
        uid: '123'
      };

      expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
        `${adServerUrl}/paramone=valueOne/paramtwo=valueTwo/aouserid=123`
      );
    });

    describe('should repleace URL id with cust_params ado_id (encoded string) and add uid', () => {
      test('base', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id%3DidValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerURLWithoutDefaultId}/id=idValue/aouserid=123`
        );
      });

      test('with additional params before', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'paramone%3DvalueOne%26ado_id%3DidValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue/aouserid=123`
        );
      });
      test('with additional params after', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id%3DidValue%26paramone%3DvalueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue/aouserid=123`
        );
      });
    });

    test('should repleace URL id with cust_params ado_id (decoded string) and add uid and IP', () => {
      const paramsWithUid: DaiAdsRequestCommon = {
        headers: { 'x-device-ip': '*******' },
        custParams: 'ado_id=idValue',
        uid: '123',
        ip: '*******'
      };

      expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
        `${adServerURLWithoutDefaultId}/id=idValue/aouserid=123/advid=*******`
      );
    });

    describe('should repleace URL id with cust_params ado_id (decoded string) and add uid', () => {
      test('base', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id=idValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerURLWithoutDefaultId}/id=idValue/aouserid=123`
        );
      });
      test('with additional params before', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'paramone=valueOne&ado_id=idValue',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue/aouserid=123`
        );
      });
      test('with additional params after', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'ado_id=idValue&paramone=valueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerURLWithoutDefaultId}/paramone=valueOne/id=idValue/aouserid=123`
        );
      });
    });

    describe("should not replace URL id with cust_params param that contains 'id' within it's name (decoded, 3 params) and should add uid", () => {
      test('wrong prefix', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid=valueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/fooid=valueOne/aouserid=123`
        );
      });
      test('wrong prefix and wrong suffix', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooidfoo=valueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/fooidfoo=valueOne/aouserid=123`
        );
      });
      test('wrong suffix', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'idfoo=valueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/idfoo=valueOne/aouserid=123`
        );
      });
      test('every possible wrong combination', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid=valueOne&idfoo=valueTwo&fooidfoo=valueThree',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/fooid=valueOne/idfoo=valueTwo/fooidfoo=valueThree/aouserid=123`
        );
      });
    });

    describe("should not replace URL id with cust_params param that contains 'id' within it's name (encoded, 3 params) and should add uid", () => {
      test('wrong prefix', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid%3DvalueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/fooid=valueOne/aouserid=123`
        );
      });
      test('wrong prefix and wrong suffix', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooidfoo%3DvalueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/fooidfoo=valueOne/aouserid=123`
        );
      });
      test('wrong suffix', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'idfoo%3DvalueOne',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/idfoo=valueOne/aouserid=123`
        );
      });
      test('every possible wrong combination', () => {
        const paramsWithUid: DaiAdsRequestCommon = {
          headers: {},
          custParams: 'fooid%3DvalueOne%26idfoo%3DvalueTwo%26fooidfoo%3DvalueThree',
          uid: '123'
        };

        expect(createADORequestUrl(adServerUrl, paramsWithUid)).toEqual(
          `${adServerUrl}/fooid=valueOne/idfoo=valueTwo/fooidfoo=valueThree/aouserid=123`
        );
      });
    });
  });
});
