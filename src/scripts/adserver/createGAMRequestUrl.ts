import { DaiAdsRequestCommon } from '../../interfaces';
import { URLParamsHelper } from './urlHelper';

export const createGAMRequestUrl = (
  baseConfigUrl: string,
  providerRequest: DaiAdsRequestCommon
): string => {
  const { uid, custParams } = providerRequest;

  const baseHelper = new URLParamsHelper(baseConfigUrl, '&');

  if (baseHelper.has('cust_params')) {
    const currentCustParams = baseHelper.get('cust_params')!;
    const currentCustParamsHelper = new URLParamsHelper(currentCustParams);

    baseHelper.delete('cust_params').addQueries(currentCustParamsHelper);
  }

  if (custParams) {
    const additionalParamsHelper = new URLParamsHelper(custParams);
    baseHelper.addQueries(additionalParamsHelper);
  }

  baseHelper.addMaybe('ppid', uid);

  return baseHelper.toString();
};
