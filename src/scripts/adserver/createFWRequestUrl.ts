import { URLParamsHelper } from './urlHelper';

export const createFWRequestUrl = (
  baseConfigUrl: string,
  gdpr?: string,
  custParams?: string
): string => {
  const helper = new URLParamsHelper(baseConfigUrl, '&');

  if (custParams) {
    const helper2 = new URLParamsHelper(custParams);
    helper.addQueries(helper2);
  }

  if (gdpr === '1') {
    helper.delete('_fw_is_lat');
  }

  return helper.toString();
};
