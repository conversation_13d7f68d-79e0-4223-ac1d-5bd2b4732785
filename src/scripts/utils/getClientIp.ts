/* eslint-disable prefer-arrow-callback */
import is from './is';

export function getClientIpFromXForwardedFor(value?: string): null | string {
  if (!is.existy(value)) {
    return null;
  }

  if (!is.string(value)) {
    throw new TypeError(`Expected a string, got ${typeof value}`);
  }

  const forwardedIps = value.split(',').map((e) => {
    const ip = e.trim();

    if (ip.includes(':')) {
      const splitted = ip.split(':');

      if (splitted.length === 2) {
        return splitted[0];
      }
    }

    return ip;
  });

  for (let i = 0; i < forwardedIps.length; i++) {
    if (is.ip(forwardedIps[i])) {
      return forwardedIps[i];
    }
  }

  return null;
}

export const getClientIp = (req: any): any => {
  if (req.headers) {
    if (is.ip(req.headers['x-client-ip'])) {
      return req.headers['x-client-ip'];
    }

    const xForwardedFor = getClientIpFromXForwardedFor(req.headers['x-forwarded-for']);

    if (is.ip(xForwardedFor)) {
      return xForwardedFor;
    }

    if (is.ip(req.headers['cf-connecting-ip'])) {
      return req.headers['cf-connecting-ip'];
    }

    if (is.ip(req.headers['fastly-client-ip'])) {
      return req.headers['fastly-client-ip'];
    }

    if (is.ip(req.headers['true-client-ip'])) {
      return req.headers['true-client-ip'];
    }

    if (is.ip(req.headers['x-real-ip'])) {
      return req.headers['x-real-ip'];
    }

    if (is.ip(req.headers['x-cluster-client-ip'])) {
      return req.headers['x-cluster-client-ip'];
    }

    if (is.ip(req.headers['x-forwarded'])) {
      return req.headers['x-forwarded'];
    }

    if (is.ip(req.headers['forwarded-for'])) {
      return req.headers['forwarded-for'];
    }

    if (is.ip(req.headers.forwarded)) {
      return req.headers.forwarded;
    }

    if (is.ip(req.headers['x-appengine-user-ip'])) {
      return req.headers['x-appengine-user-ip'];
    }
  }

  if (is.ip(req.socket?.remoteAddress)) {
    return req.socket.remoteAddress;
  }

  if (is.ip(req.info?.remoteAddress)) {
    return req.info.remoteAddress;
  }

  if (is.ip(req.requestContext?.identity?.sourceIp)) {
    return req.requestContext.identity.sourceIp;
  }

  if (req.headers) {
    if (is.ip(req.headers['Cf-Pseudo-IPv4'])) {
      return req.headers['Cf-Pseudo-IPv4'];
    }
  }

  if (is.existy(req.raw)) {
    return getClientIp(req.raw);
  }

  return null;
};
