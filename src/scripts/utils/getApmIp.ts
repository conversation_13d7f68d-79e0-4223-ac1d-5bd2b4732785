import { FastifyRequest } from 'fastify';
import { getClientIp, getClientIpFromXForwardedFor } from './getClientIp';

export function getApmIp(req: FastifyRequest, queryIp?: string) {
  if (queryIp) return queryIp;

  const { headers } = req.raw;
  if (headers['x-device-ip']) return headers['x-device-ip'];

  if (headers['x-forwarded-for']) {
    const xForwardedFor = headers['x-forwarded-for'] as string;
    const ip = getClientIpFromXForwardedFor(xForwardedFor);

    if (!ip) {
      return getClientIp(req);
    }
  }

  return getClientIp(req);
}
