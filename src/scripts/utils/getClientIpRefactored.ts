import { FastifyRequest } from 'fastify';
import is from './is';

const apmHeaders = ['x-device-ip'];
const clientHeaders = ['x-client-ip'];

const listOfOtherPossibleIPHeaders = [
  'x-forwarded-for',
  'cf-connecting-ip',
  'fastly-client-ip',
  'true-client-ip',
  'x-real-ip',
  'x-cluster-client-ip',
  'x-forwarded',
  'forwarded-for',
  'forwarded',
  'x-appengine-user-ip',
  'Cf-Pseudo-IPv4'
];

const transformToString = (ips: string | string[]): string => {
  return Array.isArray(ips) ? ips.join(',') : ips;
};

export function findIp(headerValue: string | string[] | null | undefined): string {
  if (!headerValue || headerValue.length === 0) {
    return '';
  }

  const headerAsString = transformToString(headerValue);

  const ips = headerAsString.split(',').map((headerPart) => {
    const ip = headerPart.trim();

    if (ip.includes(':')) {
      const splitted = ip.split(':');

      if (splitted.length === 2) {
        // IPv4
        return splitted[0];
      }
    }

    return ip;
  });

  return ips.find((ip) => is.ip(ip)) ?? '';
}

export const getIpFromOtherHeaders = (
  req: FastifyRequest | FastifyRequest['raw']
): string | null => {
  if (req.headers) {
    for (const header of listOfOtherPossibleIPHeaders) {
      const foundIp = findIp(req.headers[header]);

      if (foundIp !== '') {
        return foundIp;
      }
    }
  }

  if (is.ip(req.socket?.remoteAddress)) {
    return req.socket.remoteAddress;
  }

  if ('raw' in req) {
    return getIpFromOtherHeaders(req.raw);
  }

  return null;
};

export function getClientIp(req: FastifyRequest | FastifyRequest['raw']): string | null {
  clientHeaders.forEach((header) => {
    if (is.ip(req.headers?.[header])) {
      return req.headers[header];
    }
  });
  if (is.ip(req.headers?.['x-device-ip'])) {
    return req.headers['x-device-ip'];
  }

  return getIpFromOtherHeaders(req);
}

export function getApmIp(req: FastifyRequest, queryIp?: string) {
  if (queryIp) return queryIp;

  const { headers } = req.raw;
  if (headers['x-device-ip']) return headers['x-device-ip'];

  return getIpFromOtherHeaders(req);
}
