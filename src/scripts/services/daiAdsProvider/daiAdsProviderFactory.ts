import { Injectable } from '@nestjs/common';
import { AdType, BreakConnector } from 'adpod-tools';
import {
  DaiAdsProviderOutput,
  DaiAdsProviderInput,
  DaiAdsProviderOutputBreak,
  DaiAdsProviderBreakType
} from '../../../interfaces';
import { CustomParamsGenerator } from '../customParamsGenerator.service';
import { TcfService } from '../tcf.service';

@Injectable()
export class DaiAdsProviderFactory {
  constructor(
    private readonly customParams: CustomParamsGenerator,
    private readonly tcfService: TcfService
  ) {}

  async init(params: DaiAdsProviderInput): Promise<DaiAdsProviderOutput> {
    const {
      configuration: {
        id: breakId,
        duration: blockDuration,
        channel,
        version,
        adslot,
        metadata
      },
      ip,
      ua,
      headers,
      connector,
      requestMacroParams,
      custParams
    } = params;

    const { gdpr, gdprConsent, uid, npa } = requestMacroParams;
    const workerConfigDeapProfiles = !!metadata?.deapProfiles;

    return {
      request: {
        headers,
        ip,
        ua,
        gdpr,
        gdprConsent,
        custParams: await this.customParams.generate(
          version,
          channel,
          custParams,
          uid,
          this.tcfService.deapProfilesEnabled(workerConfigDeapProfiles, npa, gdprConsent, gdpr)
        ),
        uid,
        npa
      },
      config: {
        breakId,
        blockDuration,
        connector,
        version,
        channel,
        atvSpots: adslot
          .filter((el) => el.type === AdType.atv)
          .map(({ position, duration, adrequest, type, metadata: { adId } }) => ({
            position,
            duration,
            adId,
            adrequest,
            type
          }))
      }
    };
  }

  async initBreak(params: DaiAdsProviderInput): Promise<DaiAdsProviderOutputBreak> {
    const {
      configuration: {
        id: breakId,
        duration: blockDuration,
        channel,
        version,
        adslot,
        metadata,
        breakAdRequest
      },
      headers,
      requestMacroParams,
      custParams,
      ua,
      ip
    } = params;

    const breakAdRequestParsed = requestMacroParams.applyParams(breakAdRequest ?? '', headers);
    const adUnitId = metadata?.adUnitId;
    const exactLength = metadata?.exactLenght;

    const { gdpr, gdprConsent, uid, npa } = requestMacroParams;
    const workerConfigDeapProfiles = !!metadata?.deapProfiles;

    return {
      request: {
        headers,
        gdpr,
        gdprConsent,
        custParams: await this.customParams.generate(
          version,
          channel,
          custParams,
          uid,
          this.tcfService.deapProfilesEnabled(workerConfigDeapProfiles, npa, gdprConsent, gdpr)
        ),
        uid,
        ua,
        ip,
        npa
      },
      config: {
        breakId,
        blockDuration,
        version,
        channel,
        spots: adslot
          .filter((c) => !!c.metadata.atvType?.processed?.length)
          .map(
            ({
              position,
              duration,
              type,
              metadata: { adId, slotRestrictions }
            }): DaiAdsProviderBreakType => ({
              position,
              duration,
              adId,
              type,
              slotRestrictions
            })
          ),
        breakAdRequest: breakAdRequestParsed,
        breakConnector: BreakConnector.adoceanBreakSchedule,
        adUnitId,
        exactLength: !!exactLength
      }
    };
  }
}
