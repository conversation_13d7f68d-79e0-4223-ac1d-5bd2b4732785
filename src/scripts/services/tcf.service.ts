import { Injectable } from '@nestjs/common';
import { LogLevel } from 'adpod-tools';
import { TCString } from '@iabtcf/core';
import logger from '../../libs/logging/logger';

@Injectable()
export class TcfService {
  static getGdprConsentStatus(gdprConsent?: string): {
    isValid: boolean;
    isWithPersonalizaedAdsAllowed: boolean;
  } {
    if (!gdprConsent) {
      return { isValid: false, isWithPersonalizaedAdsAllowed: false };
    }

    try {
      // [4] Use profiles to select personalised advertising
      const decodeValue = TCString.decode(gdprConsent).purposeConsents.has(4);

      logger(
        'CONSENT_STRING_VALID',
        {
          decodeValue,
          gdprConsent
        },
        LogLevel.debug
      );
      return { isValid: true, isWithPersonalizaedAdsAllowed: decodeValue };
    } catch (err: unknown) {
      logger(
        `WARN_CONSENT_STRING_INVALID`,
        {
          gdprConsent,
          err
        },
        LogLevel.warn
      );
      return { isValid: false, isWithPersonalizaedAdsAllowed: false };
    }
  }

  public deapProfilesEnabled(
    workerConfigDeapProfiles: boolean,
    npa?: '0' | '1',
    gdprConsent?: string,
    gdpr?: string
  ): boolean {
    const { isValid: isGdprConsentValid, isWithPersonalizaedAdsAllowed } =
      TcfService.getGdprConsentStatus(gdprConsent);

    const hasGdpr = !!gdpr;
    const hasGdprConsent = !!gdprConsent;
    const hasNpa = !!npa;

    const isDeapAllowedForNpaParam = !hasGdpr && !hasGdprConsent && npa !== '1';

    const isDeapAllowedForGdprParams =
      isWithPersonalizaedAdsAllowed || (gdpr === '0' && (isGdprConsentValid || !hasNpa));

    const isDeapAllowed =
      workerConfigDeapProfiles && (isDeapAllowedForNpaParam || isDeapAllowedForGdprParams);

    logger(
      `${isDeapAllowed ? 'DEBUG' : 'WARN'}_DEAP_STATUS`,
      {
        gdpr,
        gdprConsent,
        npa,
        isGdprConsentValid,
        isWithPersonalizaedAdsAllowed,
        isDeapAllowedForNpaParam,
        isDeapAllowedForGdprParams
      },
      isDeapAllowed ? LogLevel.debug : LogLevel.warn
    );

    return isDeapAllowed;
  }
}
