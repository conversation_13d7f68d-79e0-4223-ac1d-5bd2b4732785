import { Test, TestingModule } from '@nestjs/testing';

import { TcfService } from '../tcf.service';

describe('Tcf service test suite', () => {
  let tcf: TcfService;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      providers: [TcfService]
    }).compile();

    tcf = app.get(TcfService);
  });

  describe('deapProfilesEnabled test suite', () => {
    const gdprConsentWithPersonalizedAdvertisingAllowed =
      'CQBOeLAQBOeLAAcABBENA7EwAP_AAEAAAB5YJ9MB_CJ8BSFDYWJ1IIskaAUXwRABxkQhAgLBAwABiBKAOIQAkCAAAAFANCAAAAIAMHJAAAFADIAAAAAAIAgAIAAMIAAQAABKAABAAAAAAAAQCAgAAABAAQAQgmAEAAcAgAAlgAIoAFAAAAFCAACBAAAAEAAFAgkAAAAAAAAIAAAIICwABQAAjAAAAAAAABgQAAAAAAAEAAAAAAAAAAAAB78E-4F8ACgALAAcABUADgAHgAQAAyABoADwAIgATAApABVADeAHoAPwAhABHACaAE4AMMAZQA0QBzgDuAH6AP8AhABHQC-gHtASIAnYBQ4CsgFsALoAXmAy4BsYD9wIMAQrAheBPACeYE-wCB0CAABYAFQAOAAggBkAGgAPAAiABMACqAGIAN4AegA_ACaAE4AMMAZQA0QBzgDuAH6AP8AiwBHQEXgJEATsAocBbAC84GWAZcBBgcAKgAcAB4APwAoABoAEcAQgBdADBAH7gQrIQCwAFgBMACqAGIAN4AegBHADnAHcAP8CDBAAKAA8ANABfQGCAQrJQDQAFgAcAB4AEQAJgAVQAxACOAReAkQBbAC8wIMEgAYA7gGWAP3KQGwAFgAVAA4ACCAGQAaAA8ACIAEwAKQAVQAxAB-AGUANEAc4A_QCLAEdAPaAi8BIgCdgFDgLYAXnAywDLgIMFABQAjgBOADuALqAf8BUgC6AGCAP3AhW.f_gACAAAAAAA';
    const gdprConsentWithPersonalizedAdvertisingForbidden =
      'CPlyCEAPlyCEAAcABAENCzCgAMIAAAAAAAAAAAAAAAAA.YAAAAAAAAAAA';

    const gdprConsentInvalid =
      'CPlyC____INVALID____EAPlyCEAAcABAENCzCgAMIAAAAAAAAAAAAAAAAA.YAAAAAAAAAAA';

    it('should enable deap profiles if wokerConfig deapProfiles enabled and gdprConsent allows personalised advertising', async () => {
      const result = tcf.deapProfilesEnabled(
        true,
        undefined,
        gdprConsentWithPersonalizedAdvertisingAllowed
      );
      expect(result).toBeTruthy();
    });

    it("shouldn't enable deap profiles if wokerConfig deapProfiles enabled and gdprConsent forbids personalised advertising and gdpr=1", async () => {
      const result = tcf.deapProfilesEnabled(
        true,
        undefined,
        gdprConsentWithPersonalizedAdvertisingForbidden,
        '1'
      );
      expect(result).toBeFalsy();
    });

    it('should enable deap profiles if wokerConfig deapProfiles enabled and gdprConsent forbids personalised advertising and gdpr=0', async () => {
      const result = tcf.deapProfilesEnabled(
        true,
        undefined,
        gdprConsentWithPersonalizedAdvertisingForbidden,
        '0'
      );
      expect(result).toBeTruthy();
    });

    it('should enable deap profiles if wokerConfig deapProfiles enabled and gdprConsent forbids personalised advertising and gdpr undefined', async () => {
      const result = tcf.deapProfilesEnabled(
        true,
        undefined,
        gdprConsentWithPersonalizedAdvertisingForbidden,
        undefined
      );
      expect(result).toBeFalsy();
    });

    it('should enable deap profiles if wokerConfig deapProfiles disabled and gdprConsent allows personalised advertising', async () => {
      const result = tcf.deapProfilesEnabled(
        false,
        undefined,
        gdprConsentWithPersonalizedAdvertisingAllowed
      );
      expect(result).toBeFalsy();
    });

    it("shouldn't enable deap profiles if wokerConfig deapProfiles disabled and gdprConsent forbids personalised advertising", async () => {
      const result = tcf.deapProfilesEnabled(
        false,
        undefined,
        gdprConsentWithPersonalizedAdvertisingForbidden
      );
      expect(result).toBeFalsy();
    });

    it('should enable deap profiles if wokerConfig deapProfiles enabled and gdprConsent not provided', async () => {
      const result = tcf.deapProfilesEnabled(true);
      expect(result).toBeTruthy();
    });

    it("shouldn't enable deap profiles if wokerConfig deapProfiles disabled and gdprConsent not provided", async () => {
      const result = tcf.deapProfilesEnabled(false);
      expect(result).toBeFalsy();
    });

    it('should enable deap profiles if wokerConfig deapProfiles enabled and gdprConsent invalid', async () => {
      const result = tcf.deapProfilesEnabled(true, undefined, gdprConsentInvalid);
      expect(result).toBeFalsy();
    });
  });
});
