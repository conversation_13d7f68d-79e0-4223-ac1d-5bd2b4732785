import { Protocol } from '../../../models/protocol.model';
import { RequestMacroParams } from './requestMacroParams';

import { ParamsMacro } from 'adpod-tools';
import { RequestHeaders } from '../../../interfaces';

describe('RequestMacroParams', () => {
  describe('applyParams', () => {
    const headers: RequestHeaders = {
      'x-device-user-agent': 'USER-AGENT',
      'x-device-ip': '*******',
      'x-device-referrer': 'REFERER'
    };

    it('should return the same string when no macros are present', () => {
      const str = 'This is a test string';
      const requestParams = new RequestMacroParams(undefined, Protocol.http);
      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('This is a test string');
    });

    it('should replace macros in the string with corresponding values from requestParams', () => {
      const str = `Hello, ${ParamsMacro.uid}!`;
      const requestParams = new RequestMacroParams('12345', Protocol.http);
      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('Hello, 12345!');
    });

    it('should handle empty or null values in requestParams and extraParams', () => {
      const str = `Hello, ${ParamsMacro.uid}!`;
      const requestParams = new RequestMacroParams(undefined, Protocol.http);
      const extraParams = { uid: '' };
      const result = requestParams.applyParams(str, headers, extraParams);
      expect(result).toBe('Hello, !');
    });

    it('should generate and replace rand8 with a random number within the specified range', () => {
      const str = `Example string with rand8: ${ParamsMacro.rand8}`;
      const requestParams = new RequestMacroParams(
        undefined,
        Protocol.http,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        'rand8Value'
      );
      const extraParams = { extra: 'extraValue' };
      const result = requestParams.applyParams(str, headers, extraParams);
      const rand8Regex = /Example string with rand8: \d{8}/;
      expect(result).toMatch(rand8Regex);
    });

    it('should remove gdpr patterns from custParams when present', () => {
      const str = `Sample string with custParams=${ParamsMacro.custParams}`;
      const requestParams = new RequestMacroParams(
        undefined,
        Protocol.http,
        undefined,
        'gdpr_consent%3D123456%26gdpr%3D1'
      );
      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('Sample string with custParams=');
    });

    it('should remove gdpr patterns from custParams when present and preserve other params', () => {
      const str = `custParams=${ParamsMacro.custParams}`;
      const requestParams = new RequestMacroParams(
        undefined,
        Protocol.http,
        undefined,
        'gdpr_consent=123456&gdpr=1&a=0'
      );

      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('custParams=a%3D0');
    });

    it('should replace ch parameter correctly', () => {
      const str = `Hello, ${encodeURIComponent(ParamsMacro.channel)}`;
      const requestParams = new RequestMacroParams(
        undefined,
        Protocol.http,
        undefined,
        undefined,
        'example&value!'
      );
      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('Hello, example&value!');
    });

    it('should merge requestParams and extraParams correctly', () => {
      const str = `Hello, ${ParamsMacro.uid}!`;
      const requestParams = new RequestMacroParams(
        '12345',
        Protocol.http,
        undefined,
        'gdpr_consent%3DABC123%26gdpr%3D1',
        'channel123',
        '1',
        undefined,
        '12345678'
      );
      const extraParams = {
        gdpr: 'false',
        custParams: 'gdpr_consent%3D!@#abc%26gdpr%3D0',
        rand8: '87654321',
        ch: 'channel456'
      };
      const result = requestParams.applyParams(str, headers, extraParams);
      expect(result).toBe('Hello, 12345!');
    });

    it('should handle undefined extraParams by not applying any additional parameters', () => {
      const str = `Hello, ${ParamsMacro.uid}!`;
      const requestParams = new RequestMacroParams('12345', Protocol.http);
      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('Hello, 12345!');
    });

    it('should handle multiple occurrences of the same macro in the string', () => {
      const str = `Hello, ${ParamsMacro.uid}! My ID is ${ParamsMacro.uid}.`;
      const requestParams = new RequestMacroParams('54321', Protocol.http);
      const result = requestParams.applyParams(str, headers);
      expect(result).toBe('Hello, 54321! My ID is 54321.');
    });

    it('should verify ParamsMacro keys before replacements', () => {
      const str = `Hello, ${ParamsMacro.uid}!`;
      const requestParams = new RequestMacroParams('12345', Protocol.http);
      const extraParams = { custParams: 'gdpr_consent%3Dtrue%26gdpr%3D1' };
      const result = requestParams.applyParams(str, headers, extraParams);
      expect(result).toBe('Hello, 12345!');
    });
  });

  test('properly sets properties, npa = 1', () => {
    const injectedRouteParams = new RequestMacroParams(
      'UID',
      Protocol.http,
      '1',
      'cust_params'
    );
    expect(injectedRouteParams.custParams).toBe('cust_params');
    expect(injectedRouteParams.uid).toBe('UID');
    expect(injectedRouteParams.npa).toBe('1');
  });
  test('properly sets properties, npa = 0', () => {
    const injectedRouteParams = new RequestMacroParams(
      'UID',
      Protocol.http,
      '0',
      'cust_params'
    );
    expect(injectedRouteParams.custParams).toBe('cust_params');
    expect(injectedRouteParams.uid).toBe('UID');
    expect(injectedRouteParams.npa).toBe('0');
  });
  test('properly sets properties, npa = undefined', () => {
    const injectedRouteParams = new RequestMacroParams(
      'UID',
      Protocol.http,
      undefined,
      'cust_params'
    );
    expect(injectedRouteParams.custParams).toBe('cust_params');
    expect(injectedRouteParams.uid).toBe('UID');
    expect(injectedRouteParams.npa).toBe(undefined);
  });
});
