import { ParamsMacro } from 'adpod-tools';
import { InjectedParams, RequestHeaders } from '../../../interfaces';
import { Protocol } from '../../../models/protocol.model';
import { URLParamsHelper } from '../../adserver/urlHelper';
import { isArray, random } from 'lodash';

export type ParamType = string | null | undefined;

export class RequestMacroParams {
  constructor(
    public uid: string | undefined,
    public requestProtocol: Protocol,
    public npa?: '1' | '0',
    public custParams?: string,
    public ch?: string,
    public gdpr?: string,
    public gdprConsent?: string,
    public rand8?: ParamType
  ) {}

  private buildReqParams(
    headers: RequestHeaders,
    ip?: string,
    ua?: string | string[]
  ): InjectedParams {
    // IncomingHttpHeaders
    return {
      uid: this.uid,
      gdprConsent: this.gdprConsent,
      gdpr: this.gdpr,
      npa: this.npa,
      custParams: this.custParams ? encodeURIComponent(this.custParams) : '',
      deviceUserAgent: ua ? encodeURIComponent(isArray(ua) ? ua[0] : ua) : undefined,
      deviceIP: ip,
      deviceReferrer: headers['x-device-referrer'],
      rand8: null,
      httpProtocol: this.requestProtocol,
      ch: this.ch,
      appVersion: process.env.npm_package_version
    };
  }

  applyParams(
    str: string,
    headers: RequestHeaders, // IncomingHttpHeaders
    extraParams?: Record<string, unknown>,
    ip?: string,
    ua?: string | string[]
  ): string {
    let strCopy = str;

    const requestParams = this.buildReqParams(headers, ip, ua);
    const mergedParams = { ...requestParams, ...extraParams };

    for (const [key, value] of Object.entries(mergedParams)) {
      let macroValue = value || '';

      switch (key) {
        case 'custParams':
          const helper = new URLParamsHelper(macroValue, '%26');
          helper.delete('gdpr_consent');
          helper.delete('gdpr');
          macroValue = helper.toString();
          strCopy = strCopy.replaceAll(ParamsMacro.custParams, macroValue);
          break;
        case 'rand8':
          strCopy = strCopy.replaceAll(ParamsMacro.rand8, `${random(10000000, 99999999)}`);
          break;
        case 'ch':
          const encodedChannelParam = encodeURIComponent(ParamsMacro.channel);
          strCopy = strCopy.replaceAll(encodedChannelParam, macroValue);
          break;
        default:
          strCopy = strCopy.replaceAll(ParamsMacro[key], macroValue);
      }
    }

    return strCopy;
  }
}
