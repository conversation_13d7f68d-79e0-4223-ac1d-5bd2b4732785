import dayjs from 'dayjs';
import { getPastDates } from '../date/getPastDates';
import { validators } from '../../EnvValidation/envalidConfig';

// amount of days that worker uses for stronig configs in memory
export const cacheSizeDays = validators.CACHE_SIZE_DAYS;

// array of dates used by cache
export const cacheDates = (): string[] => {
  const cachePastDates = getPastDates(cacheSizeDays, dayjs().add(1, 'day'));

  if (dayjs().hour() >= 22) {
    return [...cachePastDates, dayjs().add(1, 'day').format('YYYYMMDD')].sort();
  }

  return cachePastDates.sort();
};

// array of dates avaliable for Bucket search
// e.g. fetch config that is not present in memory cache
const cacheBucketFetchDaysLimit = validators.CACHE_BUCKET_FETCH_DAYS_LIMIT;

// return array of dates avaliable to Bucket search
// e.g for paramters:
// assuming today's date is 20121008
// memory cache (cacheDates) -> [20121008, 20121007]
// (bucketDatesToFetch) -> [20121006, 20121005, 20121004, 20121003, 20121002, 20121001, 20120930]
export const bucketDatesToFetch = (): string[] =>
  getPastDates(cacheBucketFetchDaysLimit, dayjs().subtract(cacheSizeDays - 1, 'day'));
