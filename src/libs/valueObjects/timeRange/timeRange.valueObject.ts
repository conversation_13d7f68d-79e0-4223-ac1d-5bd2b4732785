/* eslint-disable consistent-return */
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(isBetween);
dayjs.extend(isSameOrBefore);
dayjs.extend(timezone);
dayjs.extend(utc);

export class TimeRange {
  private readonly defaultFormat = 'YYYYMMDD_HH:mm:ss';

  constructor(
    public readonly start: dayjs.Dayjs,
    public readonly end: dayjs.Dayjs
  ) {}

  static create(start: dayjs.Dayjs, end: dayjs.Dayjs) {
    if (end.isBefore(start)) {
      throw new Error('End date must be after or equal to start date');
    }

    return new TimeRange(start, end);
  }

  static today() {
    return new TimeRange(dayjs().startOf('day'), dayjs().endOf('day'));
  }

  public isDateInTimeRange(date: string | dayjs.Dayjs): boolean {
    const dayjsDate = date instanceof dayjs ? date : dayjs(date);
    return dayjsDate.isBetween(this.start, this.end);
  }

  public isContainedIn(other: TimeRange): boolean {
    return (
      this.start.isBetween(other.start, other.end, null, '[]') &&
      this.end.isBetween(other.start, other.end, null, '[]')
    );
  }

  public isOverlapping(other: TimeRange): boolean {
    return this.start.isBefore(other.end) && this.end.isAfter(other.start);
  }

  public getDuration(unit: dayjs.QUnitType | dayjs.OpUnitType): number {
    return this.end.diff(this.start, unit);
  }

  public getAllDatesInRange(
    unit: dayjs.ManipulateType,
    format = this.defaultFormat
  ): string[] {
    const result: string[] = [];
    let current = this.start.clone().startOf(unit);

    while (current.isSameOrBefore(this.end)) {
      result.push(current.format(format));
      current = current.add(1, unit);
    }

    return result;
  }

  public toFormat(format = this.defaultFormat) {
    return {
      start: this.start.format(format),
      end: this.end.format(format)
    };
  }
}
