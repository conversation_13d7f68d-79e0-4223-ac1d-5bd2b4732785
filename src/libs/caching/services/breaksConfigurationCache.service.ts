import { Injectable } from '@nestjs/common';
import { Channel, IConfiguration, LogLevel } from 'adpod-tools';
import logger from '../../logging/logger';
import { ICacheProvider } from '../cache.provider';
import dayjs from 'dayjs';
import { TimeRange } from '../../valueObjects';

@Injectable()
export class BreaksConfigurationCacheService {
  private readonly cacheBreakKeys = 'CACHE_BREAK_KEYS';

  constructor(private readonly cacheManager: ICacheProvider) {}

  public async getConfigsFromCache(timeRange: TimeRange, channel: Channel, version: string) {
    const allDatesInRange = timeRange.getAllDatesInRange('hours');
    const keys = allDatesInRange.map((date) => `${date}_${channel}_${version}`);
    const configs = (await this.cacheManager.getMany<IConfiguration>(keys))
      .filter((config) => config)
      .flat();

    logger(
      'BREAKS_CONFIGURATION_SERVICE_GET_CONFIGS_FROM_CACHE',
      { configsCount: configs.length, keys },
      LogLevel.cache
    );

    return configs;
  }

  public async setConfigToCache(
    configs: IConfiguration[],
    shouldSetToCache: boolean,
    ttl?: number
  ) {
    if (!shouldSetToCache || configs.length === 0) {
      return [];
    }

    const configsGroupedByChannelAndVersion = configs.reduce<Record<string, IConfiguration[]>>(
      (acc, config) => {
        const key = this.createCacheKey(config);
        acc[key] = acc[key] ?? [];
        acc[key].push(config);

        return acc;
      },
      {}
    );

    const cacheValues = Object.entries(configsGroupedByChannelAndVersion).map(
      ([key, value]) => ({
        key,
        value,
        ttl: ttl && ttl * 60 * 60 * 1000
      })
    );

    logger(
      'BREAKS_CONFIGURATION_SERVICE_SET_TO_CACHE',
      { configs: cacheValues.map(({ key, value }) => ({ key, value: value.length })) },
      LogLevel.cache
    );

    await this.cacheManager.setMany(cacheValues);

    return cacheValues;
  }

  public createCacheKey(config: IConfiguration) {
    const time = dayjs(config.time)
      .set('minutes', 0)
      .set('seconds', 0)
      .format('YYYYMMDD_HH:mm:ss');

    return `${time}_${config.channel}_${config.version}`;
  }

  public async rotateCacheBreakKeys(newCacheKeys: string[]) {
    const oldCacheKeys = (await this.cacheManager.get<string[]>(this.cacheBreakKeys)) ?? [];

    await this.cacheManager.set(this.cacheBreakKeys, newCacheKeys);

    const cacheKeysToRemove = oldCacheKeys.filter((key) => !newCacheKeys.includes(key));

    logger(
      'BREAKS_CONFIGURATION_SERVICE_SET_TO_CACHE',
      { configsToDelete: cacheKeysToRemove },
      LogLevel.cache
    );
    await this.cacheManager.deleteMany(cacheKeysToRemove);
  }
}
