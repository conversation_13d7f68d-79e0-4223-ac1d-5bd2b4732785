import { Injectable, OnModuleInit } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { LogLevel } from 'adpod-tools';
import logger from '../libs/logging/logger';
import { validators } from '../EnvValidation/envalidConfig';
import { getCacheRandomRefreshInterval } from '../scripts/utils/getCacheRandomRefreshInterval';
import {
  BreaksConfigurationCacheService,
  ICacheProvider,
  WorkerConfigCacheService
} from '../libs/caching';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { AWSS3FileContent, AWSS3List, IRedisClient } from 'adpod-aws';

dayjs.extend(isBetween);

const intervalValue = getCacheRandomRefreshInterval();
const intervalValueOffset = validators.REFRESH_CACHE_INTERVAL_OFFSET;
const DeapTimestampKey = 'deapLastUpdateTimestamp';

@Injectable()
export class UpdateService implements OnModuleInit {
  constructor(
    private readonly cacheManager: ICacheProvider,
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly workerConfigCacheService: WorkerConfigCacheService,
    private readonly redisClient: IRedisClient,
    private readonly awsS3FileContent: AWSS3FileContent,
    private readonly awsS3List: AWSS3List
  ) {}

  async onModuleInit(): Promise<void> {
    logger(
      'CACHE_START_SCHEDULE',
      { intervalValue, intervalValueOffset },
      LogLevel.configsCache
    );
    await this.update();
    await this.updateCacheWithRedisUpdateStatus();
  }

  @Interval(intervalValue)
  async update(): Promise<void> {
    await this.redisConnectionConfigSetup();
    const timeRange =
      await this.workerConfigCacheService.getScheduleConfigsAvailabilityTimeRange();
    const rangeDayDates = timeRange.getAllDatesInRange('days', 'YYYYMMDD');

    logger('CACHE_START_UPDATE_PROCESS', { rangeDayDates }, LogLevel.configsCache);

    const configKeys = await this.fetchConfigKeysFromBucket(rangeDayDates);

    const promises = configKeys
      .filter(({ keys }) => keys.length > 0)
      .map(async ({ date, keys }) => {
        const keysToTransform = this.getKeys(keys);
        const configs = await this.getConfigsFromBucket(keysToTransform);

        logger(
          'CACHE_CONFIGS_BEFORE_FILTER',
          { date, count: configs.length },
          LogLevel.configsCache
        );

        const filteredConfigs = configs.filter((config) =>
          timeRange.isDateInTimeRange(config.time)
        );
        const cacheConfigs = await this.breaksConfigurationCacheService.setConfigToCache(
          filteredConfigs,
          true
        );

        const cacheKeys = cacheConfigs.map(({ key }) => key);
        return cacheKeys;
      });

    const cacheKeys = (await Promise.all(promises)).flat();
    await this.breaksConfigurationCacheService.rotateCacheBreakKeys(cacheKeys);

    logger('CACHE_FINISH_UPDATE_PROCESS', { rangeDayDates }, LogLevel.configsCache);
  }

  @Interval(validators.REDIS_CLUSTER_UPDATE_STATUS_RETRY_INTERVAL)
  async updateCacheWithRedisUpdateStatus(): Promise<void> {
    logger('CACHE_START_REDIS_UPDATE_STATUS_START', undefined, LogLevel.configsCache);

    const status = await this.redisClient.get(validators.REDIS_CLUSTER_UPDATE_STATUS_KEY);

    if (status) {
      await this.cacheManager.set('redisClusterUpdateStatus', status);
    }

    logger('CACHE_START_REDIS_UPDATE_STATUS_END', undefined, LogLevel.configsCache);
  }

  private async redisConnectionConfigSetup(): Promise<void> {
    logger('REDIS_ENV_CONFIG_IS_SET', {}, LogLevel.dev);
    try {
      let currentDeapUpdateTimestamp = await this.cacheManager.get<string>(DeapTimestampKey);

      const deapLastUpdateTimestamp = await this.redisClient.get<string>('update:timestamp');

      if (currentDeapUpdateTimestamp === undefined) {
        logger('CACHE_DEAP_UPDATE_TIMESTAMP_SET', { deapLastUpdateTimestamp }, LogLevel.cache);
        currentDeapUpdateTimestamp = deapLastUpdateTimestamp || undefined;
      }

      if (
        currentDeapUpdateTimestamp &&
        deapLastUpdateTimestamp &&
        currentDeapUpdateTimestamp < deapLastUpdateTimestamp
      ) {
        logger(
          'CACHE_DEAP_CONFIG_RESET',
          { currentDeapUpdateTimestamp, deapLastUpdateTimestamp },
          LogLevel.cache
        );
        await this.cacheManager.clear();
      }
    } catch (err) {
      logger('ERROR_REDIS_CLIENT_CONNECTION', { err }, LogLevel.error);
    }
  }

  private async fetchConfigKeysFromBucket(dates: string[]) {
    const fetchBucketFilesForAllCacheDays = dates.map(async (date: string) => {
      return {
        date,
        keys: await this.awsS3List.getAllTheNewestKeysFromBucket(
          date,
          undefined,
          undefined,
          validators.EXCLUDE_CONFIGS_LOAD_WITH_FILENAME_CONTAINING
        )
      };
    });

    const fetchedKeysForDates = await Promise.all(fetchBucketFilesForAllCacheDays);

    logger(
      'CACHE_FETCHED_KEYS_FOR_DATE',
      {
        fetchedKeysForDates: fetchedKeysForDates.map((kd) => ({
          date: kd.date,
          keys: kd.keys.length
        }))
      },
      LogLevel.configsCache
    );
    return fetchedKeysForDates;
  }

  private getKeys(keys: string[]) {
    const { CACHE_CONFIGS_TO_SET_LOCALLY } = validators;
    return CACHE_CONFIGS_TO_SET_LOCALLY.length > 0
      ? CACHE_CONFIGS_TO_SET_LOCALLY.split(',')
      : keys;
  }

  private async getConfigsFromBucket(keys: string[]) {
    const configs = await this.awsS3FileContent.getAllFilesContent(keys);
    return configs.flat();
  }
}
