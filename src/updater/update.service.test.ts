import { Channel, IConfiguration } from 'adpod-tools';
import {
  AWSS3FileContentMock,
  AWSS3ListMock,
  TestAwsModule,
  TestRedisModule
} from 'adpod-aws/dist/testing';
import { UpdateService } from './update.service';
import { Test, TestingModule } from '@nestjs/testing';
import {
  BreaksConfigurationCacheService,
  ICacheProvider,
  WorkerConfigCacheService
} from '../libs';
import dayjs from 'dayjs';
import MockDate from 'mockdate';
import { CacheStub, TestStubCacheModule } from '../libs/testing';
import { AWSS3FileContent, AWSS3List } from 'adpod-aws';

describe('UpdateService', () => {
  let updateService: UpdateService;
  let cacheProviderStub: CacheStub;
  let awsFileContentMock: AWSS3FileContentMock;
  let awsListMock: AWSS3ListMock;

  beforeEach(async () => {
    MockDate.reset();
    const app: TestingModule = await Test.createTestingModule({
      imports: [TestStubCacheModule, TestAwsModule, TestRedisModule],
      providers: [UpdateService, BreaksConfigurationCacheService, WorkerConfigCacheService]
    }).compile();

    updateService = app.get(UpdateService);
    cacheProviderStub = app.get(ICacheProvider);
    awsFileContentMock = app.get(AWSS3FileContent);
    awsListMock = app.get(AWSS3List);

    await cacheProviderStub.set('workerConfig', {
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: true,
        outOfRangeConfigsTTL: 4
      }
    });
  });

  test('should add configs to the same key hour', async () => {
    MockDate.set(dayjs().set('hour', 12).set('minute', 0).set('second', 0).toDate());

    // arrange
    const configs = [
      {
        id: 'config-1',
        time: dayjs().subtract(15, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      },
      {
        id: 'config-2',
        time: dayjs().subtract(30, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      }
    ] as IConfiguration[];
    awsListMock.getAllTheNewestKeysFromBucket.mockResolvedValueOnce(['key-1', 'key-2']);
    awsFileContentMock.getAllFilesContent.mockResolvedValueOnce([configs]);

    // act
    await updateService.update();

    // assert
    const key = getCacheKey(configs[0]);
    expect(await cacheProviderStub.get(key)).toEqual([configs[0], configs[1]]);
  });

  test('should add configs to different keys', async () => {
    MockDate.set(dayjs().set('hour', 12).set('minute', 0).set('second', 0).toDate());

    // arrange
    const configs = [
      {
        id: 'config-1',
        time: dayjs().add(15, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      },
      {
        id: 'config-2',
        time: dayjs().subtract(30, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      }
    ] as IConfiguration[];
    awsListMock.getAllTheNewestKeysFromBucket.mockResolvedValueOnce(['key-1', 'key-2']);
    awsFileContentMock.getAllFilesContent.mockResolvedValueOnce([configs]);

    // act
    await updateService.update();

    // assert
    const firstKey = getCacheKey(configs[0]);
    const secondKey = getCacheKey(configs[1]);
    expect(await cacheProviderStub.get(firstKey)).toEqual([configs[0]]);
    expect(await cacheProviderStub.get(secondKey)).toEqual([configs[1]]);
  });

  test('should add configs in last and next time range', async () => {
    MockDate.set(dayjs().set('hour', 12).set('minute', 0).set('second', 0).toDate());

    // arrange
    const configs = [
      {
        id: 'config-1',
        time: dayjs().subtract(15, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      },
      {
        id: 'config-3',
        time: dayjs().add(4, 'hour').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      },
      {
        id: 'config-4',
        time: dayjs().subtract(5, 'hour').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      }
    ] as IConfiguration[];
    awsListMock.getAllTheNewestKeysFromBucket.mockResolvedValueOnce(['key-1', 'key-2']);
    awsFileContentMock.getAllFilesContent.mockResolvedValueOnce([configs]);

    // act
    await updateService.update();

    // assert
    const key = getCacheKey(configs[0]);
    expect(await cacheProviderStub.get(key)).toEqual([configs[0]]);
  });

  test('should add configs to different keys at 23:30', async () => {
    MockDate.set(dayjs().set('hour', 23).set('minute', 30).set('second', 0).toDate());

    // arrange
    const configs = [
      {
        id: 'config-1',
        time: dayjs().subtract(15, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      },
      {
        id: 'config-2',
        time: dayjs().add(1, 'hour').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      }
    ] as IConfiguration[];

    awsListMock.getAllTheNewestKeysFromBucket.mockResolvedValueOnce(['key-1', 'key-2']);
    awsFileContentMock.getAllFilesContent.mockResolvedValueOnce([configs]);

    // act
    await updateService.update();

    // assert
    const firstKey = getCacheKey(configs[0]);
    const secondKey = getCacheKey(configs[1]);
    expect(await cacheProviderStub.get(firstKey)).toEqual([configs[0]]);
    expect(await cacheProviderStub.get(secondKey)).toEqual([configs[1]]);
  });

  test('should remove old keys after time range', async () => {
    MockDate.set(dayjs().set('hour', 12).set('minute', 0).set('second', 0).toDate());
    const now = dayjs();

    // arrange
    const configs = [
      {
        id: 'config-1',
        time: now.subtract(15, 'minute').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      },
      {
        id: 'config-2',
        time: now.add(1.5, 'hour').toISOString(),
        channel: Channel.tvn,
        version: 'v1_0_0'
      }
    ] as IConfiguration[];

    awsListMock.getAllTheNewestKeysFromBucket.mockResolvedValue(['key-1', 'key-2']);
    awsFileContentMock.getAllFilesContent.mockResolvedValue([configs]);

    await updateService.update();

    MockDate.set(dayjs().set('hour', 12).set('minute', 50).set('second', 0).toDate());

    // act
    await updateService.update();

    // assert
    const oldKey = getCacheKey(configs[0]);
    const remainKey = getCacheKey(configs[1]);
    expect(await cacheProviderStub.get(oldKey)).toBeUndefined();
    expect(await cacheProviderStub.keys()).not.toContain(oldKey);
    expect(await cacheProviderStub.get(remainKey)).toEqual([configs[1]]);
  });

  function getCacheKey(config: IConfiguration) {
    const time = dayjs(config.time)
      .set('minutes', 0)
      .set('seconds', 0)
      .format('YYYYMMDD_HH:mm:ss');

    return `${time}_${config.channel}_${config.version}`;
  }
});
