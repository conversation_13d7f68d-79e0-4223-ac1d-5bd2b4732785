import { TcfService } from '../scripts/services/tcf.service';

export const isValidGdprConsent = (
  value: string,
  helpers: { error: (arg0: string) => unknown }
): string | unknown => {
  const { gdpr } = (helpers as any).state.ancestors[0];

  if (value && !['0', '1'].includes(gdpr)) {
    return helpers.error('withoutGdpr');
  }

  if (gdpr === '1') {
    const { isValid } = TcfService.getGdprConsentStatus(value);

    if (!isValid) {
      return helpers.error('invalid');
    }

    return value;
  }

  return value;
};
