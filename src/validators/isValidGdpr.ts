export const isValidGdpr = (
  value: string,
  helpers: { error: (arg0: string) => unknown }
): string | unknown => {
  const { gdpr_consent, npa } = (helpers as any).state.ancestors[0];

  if (['0', '1'].includes(npa)) {
    return helpers.error('npaNotAllowedWithGdpr');
  }

  if (!['0', '1'].includes(value)) {
    return helpers.error('withoutAllowedValues');
  }

  if (value !== '0' && !gdpr_consent) {
    return helpers.error('withoutGdprConsent');
  }

  return value;
};
