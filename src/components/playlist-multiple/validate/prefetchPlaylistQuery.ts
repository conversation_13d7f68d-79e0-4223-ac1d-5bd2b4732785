import { Channel } from 'adpod-tools';
import <PERSON><PERSON> from 'joi';
import { Joi<PERSON>chema, JoiSchemaOptions } from 'nestjs-joi';
import { isValidDateToken } from '../../../validators/isValidDateToken';
import { isValidUriParams } from '../../../validators/isValidUriParams';
import { PrefetchType } from '../../../models/prefetch.model';
import { PlaylistOutputs } from '../../../models/playlistOutput.model';
import { isValidDateTime } from '../../../validators/isValidDateTime';
import { isValidGdprConsent } from '../../../validators/isValidGdprConsent';
import { isValidGdpr } from '../../../validators/isValidGdpr';

export const minutesPrefetchRegExp =
  /^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1[0-3][0-9][0-9]|14[0-3][0-9]|1440)m(_debug)?$/;

const modeErrorMessage = `"mode" must be one of: [${Object.values(
  PrefetchType
)}] OR follow format Xm OR Xm_debug (where 'X' is a number between 1 and 1440)`;

@JoiSchemaOptions({
  allowUnknown: true,
  cache: true
})
export class PrefetchPlaylistQuery {
  @JoiSchema(
    Joi.valid(...Object.values(PlaylistOutputs))
      .empty('')
      .optional()
  )
  output?: PlaylistOutputs;

  @JoiSchema(
    Joi.alternatives()
      .try(
        Joi.string().valid(...Object.values(PrefetchType)),
        Joi.string().regex(minutesPrefetchRegExp)
      )
      .required()
      .label('mode')
      .messages({
        'alternatives.match': modeErrorMessage,
        'string.pattern.base': modeErrorMessage
      })
  )
  mode!: PrefetchType;

  @JoiSchema(Joi.string().min(1).required())
  ch!: Channel;

  @JoiSchema(Joi.string().empty('').optional())
  v?: string;

  @JoiSchema(Joi.string().min(1).optional())
  uid?: string;

  @JoiSchema(Joi.valid('1', '0').empty('').optional())
  npa?: '1' | '0' | undefined;

  @JoiSchema(
    Joi.string().optional().custom(isValidGdpr).messages({
      withoutGdprConsent:
        "'gdpr_consent' parameter is mandatory if the 'gdpr' parameter is included",
      withoutAllowedValues: "allowed valuse are '0' and '1'",
      npaNotAllowedWithGdpr: "'gdpr' cannot be used together with 'npa'"
    })
  )
  gdpr?: string;

  @JoiSchema(
    Joi.string().optional().custom(isValidGdprConsent).messages({
      withoutGdpr: "'gdpr' parameter is mandatory if the 'gdpr_consent' parameter is included",
      invalid: "'gdpr_consent' is invalid"
    })
  )
  gdpr_consent?: string;

  @JoiSchema(
    Joi.string().custom(isValidUriParams).empty('').optional().messages({
      'any.invalid': '"cust_params" must be valid string with uri params'
    })
  )
  cust_params?: string;

  @JoiSchema(
    Joi.string().length(25).custom(isValidDateTime).empty('').optional().messages({
      'any.format': 'Wrong "startDate" format. Use YYYY-MM-DDTHH:MM:SS+XX:00 format'
    })
  )
  startDate?: string;

  @JoiSchema(
    Joi.string().custom(isValidDateToken).empty('').optional().messages({
      'any.invalid': '"startDateToken" is not valid'
    })
  )
  startDateToken?: string;

  @JoiSchema(Joi.string().empty('').optional())
  startBid?: string;

  @JoiSchema(Joi.string().min(1).optional())
  duration?: string;

  @JoiSchema(Joi.string().optional())
  ip?: string;

  @JoiSchema(Joi.string().optional())
  ua?: string;
}
