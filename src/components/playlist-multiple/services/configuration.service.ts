import { Injectable } from '@nestjs/common';
import { Channel, IConfiguration, LogLevel } from 'adpod-tools';
import dayjs from 'dayjs';
import {
  BreaksConfigurationCacheService,
  TimeRange,
  WorkerConfigCacheService
} from '../../../libs';
import { validators } from '../../../EnvValidation/envalidConfig';
import logger from '../../../libs/logging/logger';
import { AWSS3FileContent } from 'adpod-aws';

export abstract class IConfigurationService {
  abstract getConfigurations(
    startDate: dayjs.Dayjs,
    channel: Channel,
    version: string,
    minutes: number,
    allowAnyStartDate: boolean,
    isPrefetchNextMode: boolean,
    startBid?: string
  ): Promise<IConfiguration[]>;
}

@Injectable()
export class ConfigurationService implements IConfigurationService {
  constructor(
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly workerConfigCacheService: WorkerConfigCacheService,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  public async getConfigurations(
    startDate: dayjs.Dayjs,
    channel: Channel,
    version: string,
    minutes: number,
    allowAnyStartDate: boolean,
    isPrefetchNextMode: boolean,
    startBid?: string
  ): Promise<IConfiguration[]> {
    const { outOfRangeConfigsTTL, keepOutOfRangeConfigs } =
      await this.workerConfigCacheService.getScheduleConfigsAvailability();

    const workerConfigTimeRange =
      await this.workerConfigCacheService.getScheduleConfigsAvailabilityTimeRange();
    const prefetchTimeRange = this.getPrefetchTimeRange(startDate, minutes);

    const isStartBidEnabled = !!startBid && validators.START_BID_S3_FETCH === 'ENABLED';
    logger('PREFETCH_IS_START_BID_ENABLED', { isStartBidEnabled }, LogLevel.debug);

    if (isStartBidEnabled) {
      return this.handleStartBidEnabled(
        isPrefetchNextMode,
        minutes,
        channel,
        version,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL,
        startBid
      );
    }

    if (isPrefetchNextMode) {
      return this.handleNextPrefetch(
        isPrefetchNextMode,
        prefetchTimeRange,
        channel,
        version,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    const isInTimeRange = prefetchTimeRange.isContainedIn(workerConfigTimeRange);
    const shouldGetConfigsFromCache = isInTimeRange && !allowAnyStartDate;

    logger(
      'PREFETCH_SHOULD_GET_CONFIGS_FROM_CACHE',
      { isInTimeRange, shouldGetConfigsFromCache },
      LogLevel.debug
    );

    if (shouldGetConfigsFromCache) {
      return this.handleConfigsFromCache(prefetchTimeRange, channel, version);
    }

    return this.handleConfigsFromBucket(
      prefetchTimeRange,
      channel,
      version,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );
  }

  private async getConfigsFromBucket(timeRange: TimeRange, channel: Channel, version: string) {
    const dates = timeRange.getAllDatesInRange('days', 'YYYYMMDD');
    const configs = await Promise.all(
      dates.map(async (date) =>
        this.awsS3FileContent.getConfigFromBucket(date, channel, version)
      )
    );

    const configsFromBucket = configs.filter((config) => !!config).flat();
    logger(
      'PREFETCH_CONFIGS_FROM_BUCKET',
      { configsCount: configsFromBucket.length, dates },
      LogLevel.debug
    );

    return configsFromBucket;
  }

  private getPrefetchTimeRange(startDate: dayjs.Dayjs, minutes: number) {
    const endDate = startDate.add(minutes, 'minutes');
    const timeRange = TimeRange.create(startDate, endDate);

    logger('PREFETCH_TIME_RANGE', { prefetchTimeRange: timeRange.toFormat() }, LogLevel.debug);

    return timeRange;
  }

  private async getConfigsWithStartBid(
    channel: Channel,
    version: string,
    minutes: number,
    singleConfig: boolean,
    startBid: string
  ) {
    const cacheBucketFetchDaysLimit = validators.CACHE_BUCKET_FETCH_DAYS_LIMIT;
    const pastTimeRange = TimeRange.create(
      dayjs().subtract(cacheBucketFetchDaysLimit, 'day'),
      dayjs()
    );

    logger(
      'PREFETCH_START_BID_PAST_TIME_RANGE',
      { pastTimeRange: pastTimeRange.toFormat(), singleConfig },
      LogLevel.debug
    );

    const configurations = await this.getConfigsFromBucket(pastTimeRange, channel, version);

    const filteredConfigs = this.filterConfigsForStartBid(
      startBid,
      minutes,
      configurations,
      singleConfig
    );

    logger(
      'PREFETCH_START_BID_FILTERED_CONFIGS',
      { configsCount: filteredConfigs.length },
      LogLevel.debug
    );

    return filteredConfigs;
  }

  private filterConfigsForStartBid(
    startBid: string,
    minutes: number,
    configurations: IConfiguration[],
    singleConfig: boolean
  ) {
    const startBidIndex = configurations.findIndex((c) => c.id === startBid);

    if (startBidIndex === -1) {
      logger('PREFETCH_NOT_FOUND_START_BID_IN_CONFIGURATIONS', { startBid }, LogLevel.debug);
      return [];
    }

    if (singleConfig) {
      return configurations.slice(startBidIndex + 1, startBidIndex + 2);
    }

    const startAt = dayjs(configurations[startBidIndex].time);
    const timeRange = TimeRange.create(startAt, startAt.add(minutes, 'minutes'));
    logger(
      'PREFETCH_START_BID_TIME_RANGE',
      { timeRange: timeRange.toFormat() },
      LogLevel.debug
    );

    return configurations.filter((configuration) => {
      return timeRange.isDateInTimeRange(configuration.time);
    });
  }

  private sortByTime(configs: IConfiguration[]) {
    return configs.sort((a, b) => {
      return dayjs(a.time).diff(dayjs(b.time));
    });
  }

  private async handleNextPrefetch(
    isPrefetchNextMode: boolean,
    prefetchTimeRange: TimeRange,
    channel: Channel,
    version: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    logger('IS_PREFETCH_NEXT_MODE', { isPrefetchNextMode }, LogLevel.debug);
    let configurations = (
      await this.breaksConfigurationCacheService.getConfigsFromCache(
        prefetchTimeRange,
        channel,
        version
      )
    )
      .filter((configuration) => prefetchTimeRange.isDateInTimeRange(configuration.time))
      .slice(0, 1);

    if (configurations.length === 0) {
      const configsFromBucket = await this.getConfigsFromBucket(
        prefetchTimeRange,
        channel,
        version
      );

      configurations = configsFromBucket
        .filter((configuration) => prefetchTimeRange.isDateInTimeRange(configuration.time))
        .slice(0, 1);

      await this.breaksConfigurationCacheService.setConfigToCache(
        configurations,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    return this.sortByTime(configurations);
  }

  private async handleConfigsFromCache(
    prefetchTimeRange: TimeRange,
    channel: Channel,
    version: string
  ) {
    logger('PREFETCH_GETTING_CONFIGS_FROM_CACHE', undefined, LogLevel.debug);

    const configurations = (
      await this.breaksConfigurationCacheService.getConfigsFromCache(
        prefetchTimeRange,
        channel,
        version
      )
    ).filter((configuration) => prefetchTimeRange.isDateInTimeRange(configuration.time));

    return this.sortByTime(configurations);
  }

  private async handleStartBidEnabled(
    isPrefetchNextMode: boolean,
    minutes: number,
    channel: Channel,
    version: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number,
    startBid: string
  ) {
    logger('PREFETCH_GETTING_CONFIGS_WITH_START_BID', undefined, LogLevel.debug);
    const configurations = await this.getConfigsWithStartBid(
      channel,
      version,
      minutes,
      isPrefetchNextMode,
      startBid
    );

    await this.breaksConfigurationCacheService.setConfigToCache(
      configurations,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );

    return this.sortByTime(configurations);
  }

  private async handleConfigsFromBucket(
    prefetchTimeRange: TimeRange,
    channel: Channel,
    version: string,
    keepOutOfRangeConfigs: boolean,
    outOfRangeConfigsTTL: number
  ) {
    logger('PREFETCH_GETTING_CONFIGS_FROM_S3', undefined, LogLevel.debug);
    const configsFromBucket = await this.getConfigsFromBucket(
      prefetchTimeRange,
      channel,
      version
    );

    const configurations = configsFromBucket.filter((configuration) =>
      prefetchTimeRange.isDateInTimeRange(configuration.time)
    );

    await this.breaksConfigurationCacheService.setConfigToCache(
      configurations,
      keepOutOfRangeConfigs,
      outOfRangeConfigsTTL
    );

    logger(
      'PREFETCH_CONFIGS_RETURNED',
      { configsCount: configurations.length },
      LogLevel.debug
    );

    return this.sortByTime(configurations);
  }
}
