import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationService, IConfigurationService } from './configuration.service';
import {
  BreaksConfigurationCacheService,
  ICacheProvider,
  WorkerConfigCacheService
} from '../../../libs';
import dayjs from 'dayjs';
import { Channel, IConfiguration } from 'adpod-tools';
import getPrefetchTimeByPrefetchMode from '../../../scripts/vast/getPrefetchTimeByPrefetchMode';
import { PrefetchType } from '../../../models/prefetch.model';
import MockDate from 'mockdate';
import { CacheStub, TestStubCacheModule } from '../../../libs/testing';
import { AWSS3FileContentMock, TestAwsModule } from 'adpod-aws/dist/testing';
import { AWSS3FileContent } from 'adpod-aws';

describe('ConfigurationService', () => {
  let cacheProviderStub: CacheStub;
  let configurationService: IConfigurationService;
  let awsFileContentMock: AWSS3FileContentMock;

  const channel = Channel.tvn;
  const version = 'v1_0_0';
  const startDate = dayjs('2025-05-07T12:00:00+02:00');

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    MockDate.set('2025-05-07T12:00:00+02:00');

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestStubCacheModule, TestAwsModule],
      providers: [
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        {
          provide: IConfigurationService,
          useClass: ConfigurationService
        }
      ]
    }).compile();

    cacheProviderStub = app.get(ICacheProvider);
    awsFileContentMock = app.get(AWSS3FileContent);
    configurationService = app.get(IConfigurationService);

    await cacheProviderStub.set('workerConfig', {
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: false,
        outOfRangeConfigsTTL: 4
      }
    });
  });

  test('should return configurations when prefetch time is in range', async () => {
    // arrange
    const configs = [
      {
        id: 'config-1',
        time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-2',
        time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    await cacheProviderStub.set(createKey(startDate, channel, version), configs);

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.hours1, 4).minutes!,
      false,
      false
    );

    // assert
    expect(result).toHaveLength(2);
  });

  test('should return sorted configurations', async () => {
    // arrange
    const configs = [
      {
        id: 'config-2',
        time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-1',
        time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    await cacheProviderStub.set(createKey(startDate, channel, version), configs);

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.hours1, 4).minutes!,
      false,
      false
    );

    // assert
    expect(result).toHaveLength(2);
    expect(result[0]).toEqual(configs[1]);
    expect(result[1]).toEqual(configs[0]);
  });

  test('should return configurations when prefetch time out of range and store them in cache', async () => {
    // arrange
    await cacheProviderStub.set('workerConfig', {
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: true,
        outOfRangeConfigsTTL: 4
      }
    });
    const todayConfigs = [
      {
        id: 'config-1',
        time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-2',
        time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    const nextDayConfigs = [
      {
        id: 'config-3',
        time: startDate.add(1, 'day').add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-4',
        time: startDate.add(1, 'day').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(todayConfigs);
    awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(nextDayConfigs);

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.hours24hDebug, 4).minutes!,
      false,
      false
    );

    // assert
    expect(result).toHaveLength(2);
    const cache = await cacheProviderStub.get(
      createKey(dayjs(todayConfigs[0].time), channel, version)
    );
    expect(cache).toHaveLength(2);
    expect(cache).toMatchObject(todayConfigs);
  });

  test('should not store configurations in cache with prefetch time out of range', async () => {
    // arrange
    const todayConfigs = [
      {
        id: 'config-1',
        time: startDate.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-2',
        time: startDate.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    const nextDayConfigs = [
      {
        id: 'config-3',
        time: startDate.add(1, 'day').add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-4',
        time: startDate.add(1, 'day').add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(todayConfigs);
    awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(nextDayConfigs);

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.hours24hDebug, 4).minutes!,
      false,
      false
    );

    // assert
    expect(result).toHaveLength(2);
    const cache = await cacheProviderStub.get(
      createKey(dayjs(todayConfigs[0].time), channel, version)
    );
    expect(cache).toBeUndefined();
  });

  test('should return filtered configurations by prefetch time', async () => {
    // arrange
    const todayConfigs = [
      {
        id: 'config-1',
        time: startDate.add(5, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-2',
        time: startDate.add(10, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-3',
        time: startDate.add(1.5, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-4',
        time: startDate.add(2, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(todayConfigs);
    await cacheProviderStub.set(
      createKey(dayjs(todayConfigs[0].time), channel, version),
      todayConfigs.slice(0, 2)
    );
    await cacheProviderStub.set(
      createKey(dayjs(todayConfigs[2].time), channel, version),
      todayConfigs.slice(2)
    );

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.hours1Debug, 4).minutes!,
      false,
      false
    );

    // assert
    expect(result).toHaveLength(2);
    expect(result).toMatchObject(todayConfigs.slice(0, 2));
  });

  test('should return single configuration from cache with next prefetch', async () => {
    // arrange
    const todayConfigs = [
      {
        id: 'config-1',
        time: startDate.add(5, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-2',
        time: startDate.add(10, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-3',
        time: startDate.add(1.5, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-4',
        time: startDate.add(2, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    await cacheProviderStub.set(
      createKey(dayjs(todayConfigs[0].time), channel, version),
      todayConfigs.slice(0, 2)
    );
    await cacheProviderStub.set(
      createKey(dayjs(todayConfigs[2].time), channel, version),
      todayConfigs.slice(2)
    );

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.next, 4).minutes!,
      false,
      true
    );

    // assert
    expect(result).toHaveLength(1);
    expect(result).toMatchObject(todayConfigs.slice(0, 1));
  });

  test('should return single configuration from bucket with next prefetch', async () => {
    // arrange
    const todayConfigs = [
      {
        id: 'config-1',
        time: startDate.subtract(5, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-2',
        time: startDate.subtract(10, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-3',
        time: startDate.subtract(1.5, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      },
      {
        id: 'config-4',
        time: startDate.subtract(2, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ] as IConfiguration[];

    awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce([
      {
        id: 'config-5',
        time: startDate.add(2, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      } as IConfiguration
    ]);
    await cacheProviderStub.set(
      createKey(dayjs(todayConfigs[0].time), channel, version),
      todayConfigs.slice(0, 2)
    );
    await cacheProviderStub.set(
      createKey(dayjs(todayConfigs[2].time), channel, version),
      todayConfigs.slice(2)
    );

    // act
    const result = await configurationService.getConfigurations(
      startDate,
      channel,
      version,
      getPrefetchTimeByPrefetchMode(PrefetchType.next, 4).minutes!,
      false,
      true
    );

    // assert
    expect(result).toHaveLength(1);
    expect(result).toMatchObject([
      {
        id: 'config-5',
        time: startDate.add(2, 'hours').format('YYYY-MM-DDTHH:mm:ssZ'),
        channel,
        version
      }
    ]);
  });

  function createKey(date: dayjs.Dayjs, ch: Channel, v: string) {
    return `${date.set('minutes', 0).set('seconds', 0).format('YYYYMMDD_HH:mm:ss')}_${ch}_${v}`;
  }
});
