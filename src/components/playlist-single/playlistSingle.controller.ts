import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Query,
  Req,
  Res,
  UseInterceptors
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { FastifyReply, FastifyRequest } from 'fastify';
import { LogLevel, returnAsArray } from 'adpod-tools';
import { v4 as uuid } from 'uuid';
import { Version } from '../../models/version.model';
import { PlaylistSingleService } from './playlistSingle.service';
import { isEmptyVast } from '../../scripts/vast/isEmptyVast';
import { setPlaylistResHeaders } from '../../scripts/playlist/setPlaylistResHeaders';
import { RequestMacroParams } from '../../scripts/configuration/injectReqParams/requestMacroParams';
import { GetPlaylistQuery } from './validate/getPlaylistQuery';
import { sendResponseLogsToSnowflake } from '../../scripts/playlist/sendResponseLogsToSnowflake';
import { Protocol } from '../../models/protocol.model';
import { sendRequestLogsToSnowflake } from '../../scripts/playlist/sendRequestLogsToSnowflake';
import { GetDoc } from './playlistSingle.doc.decorator';
import { PlaylistMode } from '../../models/playlistMode.model';
import { PrerollService } from './services/preRoll.service';
import { WhatsonEnum, WhatsonService } from '../whatson/whatson.service';
import { createEmptyVast } from '../../scripts/vast/createEmptyVast';
import logger from '../../libs/logging/logger';
import {
  formatPerformanceTime,
  getCurrentPerformanceTime
} from '../../scripts/utils/performanceTime';
import { getActiveSpan, getActiveTraceId } from '../../libs/logging/trace';
import { getClientIp } from '../../scripts/utils/getClientIp';
import { TimeoutInterceptor } from '../../libs/interceptors/timeout.interceptor';
import { getApmUserAgent } from '../../scripts/utils/getApmUserAgent';
import { getApmIp } from '../../scripts/utils/getApmIp';
import { WorkerConfigType } from '../../models/workerConfig';
import { ICacheProvider } from '../../libs/caching';

@Controller('api/playlist')
@ApiTags('playlist')
@UseInterceptors(TimeoutInterceptor)
export class PlaylistSingleController {
  constructor(
    private readonly playlistSingleService: PlaylistSingleService,
    private readonly prerollService: PrerollService,
    private readonly whatsonService: WhatsonService,
    private readonly localCache: ICacheProvider
  ) {}

  @Get(['/get', 'schedule-based/get'])
  @GetDoc()
  async get(
    @Req() req: FastifyRequest,
    @Query() queryParams: GetPlaylistQuery,
    @Res() res: FastifyReply
  ): Promise<void> {
    const reqProcessingTimeStart = getCurrentPerformanceTime();

    try {
      const {
        v,
        bid,
        uid,
        npa,
        cust_params,
        ch,
        output,
        mode,
        bidDate,
        duration,
        gdpr,
        gdpr_consent,
        time,
        ip,
        ua
      } = queryParams;

      const span = getActiveSpan();

      const isPreroll =
        mode && [PlaylistMode.preroll, PlaylistMode.preroll_debug].includes(mode);

      const isDefaultGetMode = !isPreroll && !time;

      if (isDefaultGetMode && !bid) {
        throw new HttpException({ status: "'bid' param is required" }, HttpStatus.BAD_REQUEST);
      }

      const version = v || Version.base;
      const requestProtocol = (req.raw.headers['x-tvn-links-response-proto'] ||
        req.raw.headers['x-forwarded-proto'] ||
        req.protocol) as Protocol;
      const requestUrl = req.raw?.url;
      const requestHeaders = req.headers;
      const requestResponseTime = res.elapsedTime;
      const sessionId = uuid();

      const { ipLogging, snowFlakeEnabledConfigs } = await this.getRequestLoggingSettings();

      const userAgent = getApmUserAgent(req, ua);
      const ampIp = getApmIp(req, ip);

      const reqIP = getClientIp(req);
      const requestIPforSnowflake = ipLogging ? ampIp : '';
      const routeParams = new RequestMacroParams(
        uid,
        requestProtocol,
        npa,
        cust_params,
        ch,
        gdpr,
        gdpr_consent
      );

      const requestDetails = {
        ...queryParams,
        rawHeaders: req.raw.headers,
        requestUrl,
        reqIP
      };

      logger('REQUEST_DETAILS', requestDetails, LogLevel.dev);

      if (span !== null) {
        span.setTag('tag-headers', req.headers);
        span.setTag('tag-rawHeaders', req.raw.headers);
        span.setTag('tag-requestUrl', requestUrl);
        span.setTag('tag-reqIP', reqIP);
        span.setTag('tag-version', v);
        span.setTag('tag-traceId', getActiveTraceId());
      }

      if (isPreroll) {
        const whatsonResponse = await this.whatsonService.checkBreaksSchedule(ch);
        const isContent = whatsonResponse?.whatson === WhatsonEnum.content;
        // eslint-disable-next-line no-void
        void setPlaylistResHeaders(res, false);

        if (isContent) {
          const response = await this.prerollService.createPrerollResponse(
            ch,
            version,
            routeParams,
            requestHeaders,
            mode,
            whatsonResponse
          );

          if (response) {
            res.send(response);
            return;
          }
        }

        res.send(await createEmptyVast());
        return;
      }

      sendRequestLogsToSnowflake(
        sessionId,
        v!,
        ch,
        snowFlakeEnabledConfigs,
        requestUrl,
        uid,
        requestIPforSnowflake
      );

      const { playlist, emptyVastReason, playlistInfo, requestLog } =
        await this.playlistSingleService.createSingleBreakVastPlaylist(
          routeParams,
          version,
          cust_params,
          bidDate,
          ch,
          output,
          mode,
          requestHeaders,
          bid,
          duration,
          time,
          ampIp,
          userAgent
        );

      logger(
        'GET_PLAYLIST_INFO',
        {
          emptyVast: !!emptyVastReason,
          info: playlistInfo,
          ...requestLog
        },
        LogLevel.dev
      );

      logger(
        'GET_PLAYLIST_STATS',
        {
          ...sendResponseLogsToSnowflake(
            requestLog,
            requestResponseTime,
            ampIp,
            requestHeaders,
            emptyVastReason,
            playlistInfo,
            snowFlakeEnabledConfigs,
            v,
            sessionId,
            ipLogging
          )
        },
        LogLevel.dev
      );

      setPlaylistResHeaders(
        res,
        isEmptyVast(playlist),
        req.raw.headers['x-tvn-links-response-proto'],
        req.raw.headers['x-forwarded-proto'],
        requestProtocol
      );

      const playlistShortStats = returnAsArray(playlistInfo).map((el) =>
        el
          ? {
              isWithReplacedAds: el.isWithReplacedAds,
              bid: el.bid,
              breakAllAdsCount: el.breakAllAdsCount,
              breakDaiPlaylistAdsCount: el.breakDaiPlaylistAdsCount,
              connector: el.connector
            }
          : el
      );

      const processingTime = +formatPerformanceTime(
        reqProcessingTimeStart,
        getCurrentPerformanceTime()
      );

      const reqProcessingTimeRounded = Math.round(+processingTime / 10);

      logger(
        `STATS_GET_REQ_TIME_PTR_${reqProcessingTimeRounded}_V_${v}_CHANNEL_${ch}`,
        {
          processingTime,
          requestDetails,
          playlistShortStats
        },
        LogLevel.statsGet
      );

      res.send(playlist);
    } catch (e: unknown) {
      logger('ERROR_UNKNOWN_SERVER_ERROR', { e }, LogLevel.error);
      if (e instanceof HttpException) throw e;
      if (e instanceof Error) {
        throw new HttpException({ ...e }, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  private async getRequestLoggingSettings() {
    const workerConfig = await this.localCache.get<WorkerConfigType>('workerConfig');

    return {
      ipLogging: workerConfig?.snowflake?.ipLogging ?? false,
      snowFlakeEnabledConfigs: workerConfig?.snowflake?.snowFlakeEnabledConfigs ?? []
    };
  }
}
