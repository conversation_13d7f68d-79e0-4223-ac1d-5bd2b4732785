import { Test, TestingModule } from '@nestjs/testing';
import { ConfigurationService, IConfigurationService } from './configuration.service';
import {
  BreaksConfigurationCacheService,
  ICacheProvider,
  WorkerConfigCacheService
} from '../../../libs';
import dayjs from 'dayjs';
import { Channel, IConfiguration } from 'adpod-tools';
import MockDate from 'mockdate';
import { CacheStub, TestStubCacheModule } from '../../../libs/testing';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { AWSS3FileContentMock, TestAwsModule } from 'adpod-aws/dist/testing';
import { AWSS3FileContent } from 'adpod-aws';

describe('ConfigurationService', () => {
  let cacheProviderStub: CacheStub;
  let configurationService: IConfigurationService;
  let awsFileContentMock: AWSS3FileContentMock;

  const channel = Channel.tvn;
  const version = 'v1_0_0';
  let now: dayjs.Dayjs;

  beforeEach(async () => {
    jest.resetAllMocks();
    jest.clearAllMocks();

    MockDate.set('2025-05-14T12:00:00+02:00');
    now = dayjs('2025-05-14T12:00:00+02:00');

    const app: TestingModule = await Test.createTestingModule({
      imports: [TestStubCacheModule, TestAwsModule],
      providers: [
        BreaksConfigurationCacheService,
        WorkerConfigCacheService,
        {
          provide: IConfigurationService,
          useClass: ConfigurationService
        }
      ]
    }).compile();

    cacheProviderStub = app.get(ICacheProvider);
    awsFileContentMock = app.get(AWSS3FileContent);
    configurationService = app.get(IConfigurationService);

    await cacheProviderStub.set('workerConfig', {
      playlistSingleTimeThreshold: 30 * 60,
      scheduleConfigsAvailability: {
        last: 1,
        next: 2,
        keepOutOfRangeConfigs: false,
        outOfRangeConfigsTTL: 4
      }
    });
  });

  describe('by bid', () => {
    test('should return configuration when config is in time range cache', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.add(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await cacheProviderStub.set(createKey(now, channel, version), configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-1'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[0]);
    });

    test('should return configuration when config is outside of time range cache', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-2'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[1]);
    });

    test('should store all configurations in cache for the hour which bid was found', async () => {
      // arrange
      await cacheProviderStub.set('workerConfig', {
        scheduleConfigsAvailability: {
          last: 1,
          next: 2,
          keepOutOfRangeConfigs: true,
          outOfRangeConfigsTTL: 4
        }
      });

      const configs = [
        {
          id: 'config-1',
          time: now.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);
      awsFileContentMock.getConfigFromBucket.mockResolvedValue(null);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-2'
      );

      // assert
      expect(result).not.toBeNull();
      const cacheValues = await cacheProviderStub.get(
        createKey(now.subtract(30, 'minute'), channel, version)
      );
      expect(cacheValues).toHaveLength(2);
      expect(cacheValues).toEqual(configs.slice(0, 2));
    });

    test('should return null when bidDate is outside the last past dates', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.subtract(15, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-2',
        now.add(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert
      expect(result).toBeNull();
    });

    test('should return null when config is before the bidDate', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.subtract(2, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-2',
        now.subtract(30, 'minute').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert
      expect(result).toBeNull();
    });

    test('should return configuration when config is inside the bidDate', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.subtract(2, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-2',
        now.subtract(4, 'hour').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[1]);
    });

    test('should set config to cache', async () => {
      // arrange
      await cacheProviderStub.set('workerConfig', {
        playlistSingleTimeThreshold: 30 * 60,
        scheduleConfigsAvailability: {
          last: 1,
          next: 2,
          keepOutOfRangeConfigs: true,
          outOfRangeConfigsTTL: 4
        }
      });

      const configs = [
        {
          id: 'config-1',
          time: now.subtract(7, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(9, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        undefined,
        'config-2',
        now.subtract(10, 'hour').format('YYYY-MM-DDTHH:mm:ssZ')
      );

      // assert
      const cacheConfig = await cacheProviderStub.get<IConfiguration[]>(
        createKey(now.subtract(9, 'hour'), channel, version)
      );

      expect(result).not.toBeNull();
      expect(result).toEqual(configs[1]);
      expect(cacheConfig).toHaveLength(1);
      expect(cacheConfig![0].id).toEqual(configs[1].id);
    });
  });

  describe('by time', () => {
    test('should return configuration when time in range cache', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.add(45, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.subtract(20, 'minute').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      await cacheProviderStub.set(createKey(now, channel, version), configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        'now'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[1]);
    });

    test('should return configuration when time is outside of range cache', async () => {
      // arrange
      const configs = [
        {
          id: 'config-1',
          time: now.add(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(4, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        '2025-05-14T15:00:00+02:00'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[0]);
    });

    test('should return configuration when time is outside of range cache and set configs to cache', async () => {
      // arrange
      await cacheProviderStub.set('workerConfig', {
        playlistSingleTimeThreshold: 30 * 60,
        scheduleConfigsAvailability: {
          last: 1,
          next: 2,
          keepOutOfRangeConfigs: true,
          outOfRangeConfigsTTL: 4
        }
      });

      const configs = [
        {
          id: 'config-1',
          time: now.add(3, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        },
        {
          id: 'config-2',
          time: now.add(4, 'hour').format('YYYY-MM-DDTHH:mm:ssZ'),
          channel,
          version
        }
      ] as IConfiguration[];

      awsFileContentMock.getConfigFromBucket.mockResolvedValueOnce(configs);

      // act
      const result = await configurationService.getConfiguration(
        channel,
        version,
        PlaylistMode.debug,
        '2025-05-14T15:00:00+02:00'
      );

      // assert
      expect(result).not.toBeNull();
      expect(result).toEqual(configs[0]);
      const cacheConfigs = await cacheProviderStub.get(
        createKey(dayjs(configs[0].time), channel, version)
      );
      expect(cacheConfigs).toHaveLength(1);
      expect(cacheConfigs).toMatchObject([configs[0]]);
    });
  });

  function createKey(date: dayjs.Dayjs, ch: Channel, v: string) {
    return `${date.set('minutes', 0).set('seconds', 0).format('YYYYMMDD_HH:mm:ss')}_${ch}_${v}`;
  }
});
