import { Injectable } from '@nestjs/common';
import { Channel, IConfiguration, LogLevel } from 'adpod-tools';
import dayjs from 'dayjs';
import {
  BreaksConfigurationCacheService,
  TimeRange,
  WorkerConfigCacheService
} from '../../../libs';
import { validators } from '../../../EnvValidation/envalidConfig';
import logger from '../../../libs/logging/logger';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { AWSS3FileContent } from 'adpod-aws';

export abstract class IConfigurationService {
  abstract getConfiguration(
    channel: Channel,
    version: string,
    mode: PlaylistMode,
    time?: string,
    bid?: string,
    bidDate?: string
  ): Promise<IConfiguration | null>;
}

@Injectable()
export class ConfigurationService implements IConfigurationService {
  constructor(
    private readonly breaksConfigurationCacheService: BreaksConfigurationCacheService,
    private readonly workerConfigCacheService: WorkerConfigCacheService,
    private readonly awsS3FileContent: AWSS3FileContent
  ) {}

  public async getConfiguration(
    channel: Channel,
    version: string,
    mode: PlaylistMode,
    time?: string,
    bid?: string,
    bidDate?: string
  ): Promise<IConfiguration | null> {
    let configuration: IConfiguration | null = null;

    if (!bid && time) {
      logger('GET_CONFIG_BY_TIME_RANGE', undefined, LogLevel.debug);
      configuration = await this.getConfigurationByTimeRange(time, channel, version);
    }

    if (bid) {
      logger('GET_CONFIG_BY_BID', undefined, LogLevel.debug);
      configuration = await this.getConfigurationByBid(channel, version, bid, bidDate);
    }

    if (!configuration?.metadata?.hasAtvSlot && mode === PlaylistMode.default) {
      logger(
        'GET_CONFIG_NO_ATV_OR_DEFAULT',
        { hasAtvSlot: !configuration?.metadata?.hasAtvSlot, mode },
        LogLevel.debug
      );
      return null;
    }

    return configuration;
  }

  private async getConfigurationByBid(
    channel: Channel,
    version: string,
    bid: string,
    bidDate?: string
  ) {
    const cacheTimeRange =
      await this.workerConfigCacheService.getScheduleConfigsAvailabilityTimeRange();
    const { outOfRangeConfigsTTL, keepOutOfRangeConfigs, next } =
      await this.workerConfigCacheService.getScheduleConfigsAvailability();

    const cacheConfigs = await this.breaksConfigurationCacheService.getConfigsFromCache(
      cacheTimeRange,
      channel,
      version
    );
    const bidConfig = cacheConfigs.find((config) => config.id === bid);

    if (bidConfig) {
      logger('GET_FOUND_CONFIG_IN_CACHE', { bid }, LogLevel.debug);
      return bidConfig;
    }

    const cacheBucketFetchDaysLimit = validators.CACHE_BUCKET_FETCH_DAYS_LIMIT;
    const pastTimeRange = TimeRange.create(
      dayjs().subtract(cacheBucketFetchDaysLimit, 'day'),
      dayjs()
    );

    logger('GET_PAST_TIME_RANGE', { pastTimeRange: pastTimeRange.toFormat() }, LogLevel.debug);

    if (!bidDate) {
      const bidDateConfig = await this.getConfigsFromBucket(pastTimeRange, channel, version);

      logger('GET_BID_CONFIGS', { bidDateConfigsCount: bidDateConfig.length }, LogLevel.debug);

      await this.breaksConfigurationCacheService.setConfigToCache(
        bidDateConfig,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );

      const foundConfig = bidDateConfig.find((config) => config.id === bid) ?? null;
      logger(
        'GET_BID_CONFIG_FOUND',
        { foundConfig: foundConfig ? { id: foundConfig.id, time: foundConfig.time } : null },
        LogLevel.debug
      );
      return foundConfig;
    }

    if (!pastTimeRange.isDateInTimeRange(bidDate)) {
      logger(
        'GET_BID_DATE_NOT_IN_PAST_TIME_RANGE',
        { bidDate, pastTimeRange: pastTimeRange.toFormat() },
        LogLevel.debug
      );

      return null;
    }

    const bidDateRange = TimeRange.create(dayjs(bidDate), dayjs(bidDate).add(next, 'hour'));

    const bidDateConfig = await this.getConfigsFromBucket(bidDateRange, channel, version);

    logger(
      'GET_BID_DATE_CONFIGS_COUNT',
      { bidDateConfigsCount: bidDateConfig.length, bidDateRange: bidDateRange.toFormat() },
      LogLevel.debug
    );

    const foundConfig = bidDateConfig.find((config) => config.id === bid) ?? null;

    if (foundConfig) {
      await this.breaksConfigurationCacheService.setConfigToCache(
        bidDateConfig,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    logger(
      'GET_CONFIG_BY_BID_DATE',
      { foundConfig: foundConfig ? { id: foundConfig.id, time: foundConfig.time } : null },
      LogLevel.debug
    );

    return foundConfig;
  }

  private async getConfigurationByTimeRange(time: string, channel: Channel, version: string) {
    let configurations: IConfiguration[] = [];
    const { playlistSingleTimeThreshold } =
      await this.workerConfigCacheService.getWorkerConfig();

    const timeRange = this.getTimeRange(time, playlistSingleTimeThreshold);
    logger('GET_TIME_RANGE', { pastTimeRange: timeRange.toFormat() }, LogLevel.debug);

    const cacheTimeRange =
      await this.workerConfigCacheService.getScheduleConfigsAvailabilityTimeRange();

    const isTimeRangeInCache = timeRange.isContainedIn(cacheTimeRange);

    if (isTimeRangeInCache) {
      configurations = await this.breaksConfigurationCacheService.getConfigsFromCache(
        cacheTimeRange,
        channel,
        version
      );

      configurations = configurations.filter((config) =>
        timeRange.isDateInTimeRange(config.time)
      );

      logger(
        'GET_CACHE_TIME_RANGE_GOT_CONFIGS_FROM_CACHE',
        { configsCount: configurations.length },
        LogLevel.debug
      );
    }

    if (!configurations.length) {
      const { outOfRangeConfigsTTL, keepOutOfRangeConfigs } =
        await this.workerConfigCacheService.getScheduleConfigsAvailability();

      configurations = await this.getConfigsFromBucket(timeRange, channel, version);

      logger(
        'GET_TIME_RANGE_GOT_CONFIGS_FROM_BUCKET',
        { configsCount: configurations.length },
        LogLevel.debug
      );

      await this.breaksConfigurationCacheService.setConfigToCache(
        configurations,
        keepOutOfRangeConfigs,
        outOfRangeConfigsTTL
      );
    }

    return configurations[0] ?? null;
  }

  private getTimeRange(requestParamTime: string, threshold: number) {
    const time =
      requestParamTime === 'now'
        ? dayjs().tz('Europe/Berlin')
        : dayjs(requestParamTime.replace(' ', '+'));

    const timeFrom = time.subtract(threshold, 'second');
    const timeTo = time.add(threshold, 'second');

    return TimeRange.create(timeFrom, timeTo);
  }

  private async getConfigsFromBucket(timeRange: TimeRange, channel: Channel, version: string) {
    const dates = timeRange.getAllDatesInRange('days', 'YYYYMMDD');
    const configs = await Promise.all(
      dates.map(async (date) =>
        this.awsS3FileContent.getConfigFromBucket(date, channel, version)
      )
    );

    const configsFromBucket = configs.filter((config) => !!config).flat();
    logger(
      'GET_CONFIGS_FROM_BUCKET',
      { configsCount: configsFromBucket.length, dates },
      LogLevel.debug
    );

    return configsFromBucket.filter((config) => timeRange.isDateInTimeRange(config.time));
  }
}
