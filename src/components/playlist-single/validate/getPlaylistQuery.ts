import { Channel } from 'adpod-tools';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';
import <PERSON>Joi from 'joi';
import JoiDate from '@joi/date';
import { isValidDateToken } from '../../../validators/isValidDateToken';
import { isValidUriParams } from '../../../validators/isValidUriParams';
import { PlaylistMode } from '../../../models/playlistMode.model';
import { PlaylistOutputs } from '../../../models/playlistOutput.model';
import { isValidPlaylistSingleTime } from '../../../validators/isValidDateTime';
import { isValidGdpr } from '../../../validators/isValidGdpr';
import { isValidGdprConsent } from '../../../validators/isValidGdprConsent';

const Joi = BaseJoi.extend(JoiDate);

@JoiSchemaOptions({
  allowUnknown: true,
  cache: true
})
export class GetPlaylistQuery {
  @JoiSchema(Joi.string().optional())
  bid?: string;

  @JoiSchema(
    Joi.valid(...Object.values(PlaylistOutputs))
      .empty('')
      .optional()
  )
  output?: PlaylistOutputs;

  @JoiSchema(
    Joi.valid(...Object.values(PlaylistMode))
      .empty('')
      .optional()
  )
  mode?: PlaylistMode;

  @JoiSchema(Joi.string().min(1).required())
  ch!: Channel;

  @JoiSchema(Joi.string().allow('').empty('').optional())
  v?: string;

  @JoiSchema(Joi.string().min(1).optional())
  uid?: string;

  @JoiSchema(
    Joi.string().optional().custom(isValidGdpr).messages({
      withoutGdprConsent:
        "'gdpr_consent' parameter is mandatory if the 'gdpr' parameter is included",
      withoutAllowedValues: "allowed valuse are '0' and '1'",
      npaNotAllowedWithGdpr: "'gdpr' cannot be used together with 'npa'"
    })
  )
  gdpr?: string;

  @JoiSchema(
    Joi.string().optional().custom(isValidGdprConsent).messages({
      withoutGdpr: "'gdpr' parameter is mandatory if the 'gdpr_consent' parameter is included",
      invalid: "'gdpr_consent' is invalid"
    })
  )
  gdpr_consent?: string;

  @JoiSchema(Joi.valid('1', '0').empty('').optional())
  npa?: '1' | '0' | undefined;

  @JoiSchema(
    Joi.string().custom(isValidUriParams).empty('').optional().messages({
      'any.invalid': '"cust_params" must be valid string with uri params'
    })
  )
  cust_params?: string;

  @JoiSchema(Joi.date().format('YYYYMMDD').empty('').raw().optional())
  bidDate?: string;

  @JoiSchema(
    Joi.string()
      .custom(isValidDateToken)
      .when('bidDate', {
        is: Joi.exist(),
        then: Joi.required(),
        otherwise: Joi.optional()
      })
      .empty('')
      .messages({
        'any.required': 'validation failed', // Do not show property's name for security reasons
        'any.invalid': '"bidDateToken" is invalid'
      })
  )
  bidDateToken?: string;

  @JoiSchema(Joi.string().min(1).optional())
  duration?: string;

  @JoiSchema(
    Joi.string().custom(isValidPlaylistSingleTime).empty('').optional().messages({
      'any.format': 'Wrong "time" format. Use YYYY-MM-DDTHH:MM:SS+XX:00 format'
    })
  )
  time?: string;

  @JoiSchema(Joi.string().optional())
  ip?: string;

  @JoiSchema(Joi.string().optional())
  ua?: string;
}
